import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:phoenix/utils/theme_constants.dart';
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';



class FloatingActionButtonCustomized extends StatelessWidget {
  final void Function() action;

  const FloatingActionButtonCustomized({
    super.key, required this.action,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.small(
      elevation: 8,
      onPressed: () {},
      backgroundColor: ThemeConstants.floatingActionButtonColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(100)),
      ),
      child: Container(
        width: 40, // Standard FAB size
        height: 40,
        decoration: BoxDecoration(
          color: ThemeConstants.floatingActionButtonColor,
          shape: BoxShape.circle,
          boxShadow: [
            // Inner shadow - top left (lighter)
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              offset: Offset(-10, -10),
              blurRadius: 20,
              spreadRadius: -5,
              inset: true,
            ),
            // Inner shadow - bottom right (darker)
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              offset: Offset(10, 10),
              blurRadius: 20,
              spreadRadius: -5,
              inset: true,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            customBorder: CircleBorder(),
            onTap: action,
            child: Center(
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
