import 'package:intl/intl.dart';
import 'package:phoenix/features/common/position_comp_key.dart';
import 'package:phoenix/models/base_model.dart';

class PositionPnL implements BaseModel {
  final PositionCompKey positionCompositeKey;
  final String tradingSymbol;
  final PnLData realized;
  final PnLData unRealized;
  final PnLData dividend;
  final PnLData? charges;
  final int position;
  final double openCost;
  final DateTime date;

  PositionPnL({
    required this.positionCompositeKey,
    required this.tradingSymbol,
    required this.realized,
    required this.unRealized,
    required this.dividend,
    required this.charges,
    required this.position,
    required this.openCost,
    required this.date,
  });

  factory PositionPnL.fromJson(Map<String, dynamic> json) {
    return PositionPnL(
      positionCompositeKey:
          PositionCompKey.fromJson(json['position_composite_key']),
      tradingSymbol: json['trading_symbol'],
      realized: PnLData.fromJson(json['realized']),
      unRealized: PnLData.fromJson(json['un_realized']),
      charges: json['charge'] != null ? PnLData.fromJson(json['charge']) : null,
      position: json['position'],
      openCost: (json['open_cost'] as num).toDouble(),
      dividend: PnLData.fromJson(json['dividend']),
      date: DateFormat("yyyy-MM-dd").parse(json['date']),
    );
  }

  @override
  String toString() {
    return 'PositionPnL(tradingSymbol: $tradingSymbol, date: $date, '
        'realized: $realized, unRealized: $unRealized, openCost: $openCost, dividend: $dividend, ' 
        'charges: ${charges.toString()}, position: $position, '
        'positionCompositeKey: $positionCompositeKey)';
  }

  @override
  List<String> getSearchableFields() {
    return [tradingSymbol];
  }
}



class PnLData {
  final double sod;
  final double sow;
  final double som;
  final double soy;
  final double latest;

  PnLData({
    required this.sod,
    required this.sow,
    required this.som,
    required this.soy,
    required this.latest,
  });

  factory PnLData.fromJson(Map<String, dynamic> json) {
    return PnLData(
      sod: (json['sod'] as num).toDouble(),
      sow: (json['sow'] as num).toDouble(),
      som: (json['som'] as num).toDouble(),
      soy: (json['soy'] as num).toDouble(),
      latest: (json['latest'] as num).toDouble(),
    );
  }

   /// Access field by key name (like a map)
  double? getByKey(String key) {
    switch (key) {
      case 'sod':
        return sod;
      case 'sow':
        return sow;
      case 'som':
        return som;
      case 'soy':
        return soy;
      case 'latest':
        return latest;
      default:
        return null; // or throw error
    }
  }

  @override
  String toString() {
    return 'PnL(sod: $sod, sow: $sow, som: $som, soy: $soy, latest: $latest)';
  }
}
