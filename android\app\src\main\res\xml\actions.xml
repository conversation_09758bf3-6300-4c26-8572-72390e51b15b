<?xml version="1.0" encoding="utf-8"?>
<actions>
    <!-- Buy Stock Action -->
    <action intentName="com.phoenix.BUY_STOCK">
        <fulfillment urlTemplate="phoenix://voice/buy?symbol={symbol}&amp;quantity={quantity}&amp;type={orderType}">
            <parameter-mapping intentParameter="symbol" urlParameter="symbol"/>
            <parameter-mapping intentParameter="quantity" urlParameter="quantity"/>
            <parameter-mapping intentParameter="orderType" urlParameter="type"/>
        </fulfillment>
        
        <parameter name="symbol">
            <entity-set-reference entitySetId="StockSymbol"/>
        </parameter>
        <parameter name="quantity">
            <entity-set-reference entitySetId="Number"/>
        </parameter>
        <parameter name="orderType">
            <entity-set-reference entitySetId="OrderType"/>
        </parameter>
    </action>
    
    <!-- Sell Stock Action -->
    <action intentName="com.phoenix.SELL_STOCK">
        <fulfillment urlTemplate="phoenix://voice/sell?symbol={symbol}&amp;quantity={quantity}&amp;type={orderType}">
            <parameter-mapping intentParameter="symbol" urlParameter="symbol"/>
            <parameter-mapping intentParameter="quantity" urlParameter="quantity"/>
            <parameter-mapping intentParameter="orderType" urlParameter="type"/>
        </fulfillment>
        
        <parameter name="symbol">
            <entity-set-reference entitySetId="StockSymbol"/>
        </parameter>
        <parameter name="quantity">
            <entity-set-reference entitySetId="Number"/>
        </parameter>
        <parameter name="orderType">
            <entity-set-reference entitySetId="OrderType"/>
        </parameter>
    </action>
    
    <!-- Check Portfolio Action -->
    <action intentName="com.phoenix.CHECK_PORTFOLIO">
        <fulfillment urlTemplate="phoenix://voice/portfolio"/>
    </action>
    
    <!-- Get Quote Action -->
    <action intentName="com.phoenix.GET_QUOTE">
        <fulfillment urlTemplate="phoenix://voice/quote?symbol={symbol}">
            <parameter-mapping intentParameter="symbol" urlParameter="symbol"/>
        </fulfillment>
        
        <parameter name="symbol">
            <entity-set-reference entitySetId="StockSymbol"/>
        </parameter>
    </action>
</actions>