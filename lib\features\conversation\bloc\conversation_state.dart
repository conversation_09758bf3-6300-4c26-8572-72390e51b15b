import 'package:equatable/equatable.dart';
import 'package:phoenix/models/conversation_message.dart';
import 'package:phoenix/models/voice_command_model.dart';

/// Enum for conversation session status
enum ConversationStatus {
  initial,
  active,
  closed,
  error,
}

/// Enum for voice listening status
enum VoiceListeningStatus {
  idle,
  listening,
  paused,          // Natural pause detected, waiting for more speech
  finalizing,      // Finalizing input after extended pause
  speaking,
  error,
}
/// State class for the conversation BLoC
class ConversationState extends Equatable {
  final ConversationStatus status;
  final VoiceListeningStatus voiceStatus;
  final List<ConversationMessage> messages;
  final String? currentPartialText;
  final String? currentSessionId;
  final bool isSpeechRecognitionAvailable;
  final bool isMicrophoneMuted;
  final bool isTTSSpeaking;
  final String? currentSpeakingMessageId;
  final String? errorMessage;
  final VoiceCommand? lastProcessedCommand;
  final DateTime? sessionStartTime;

  const ConversationState({
    this.status = ConversationStatus.initial,
    this.voiceStatus = VoiceListeningStatus.idle,
    this.messages = const [],
    this.currentPartialText,
    this.currentSessionId,
    this.isSpeechRecognitionAvailable = false,
    this.isMicrophoneMuted = false,
    this.isTTSSpeaking = false,
    this.currentSpeakingMessageId,
    this.errorMessage,
    this.lastProcessedCommand,
    this.sessionStartTime,
  });

  /// Initial state
  factory ConversationState.initial() {
    return const ConversationState();
  }

  /// Copy with updated fields
  ConversationState copyWith({
    ConversationStatus? status,
    VoiceListeningStatus? voiceStatus,
    List<ConversationMessage>? messages,
    String? currentPartialText,
    String? currentSessionId,
    bool? isSpeechRecognitionAvailable,
    bool? isMicrophoneMuted,
    bool? isTTSSpeaking,
    String? currentSpeakingMessageId,
    String? errorMessage,
    VoiceCommand? lastProcessedCommand,
    DateTime? sessionStartTime,
    bool clearPartialText = false,
    bool clearError = false,
    bool clearSpeakingMessage = false,
  }) {
    return ConversationState(
      status: status ?? this.status,
      voiceStatus: voiceStatus ?? this.voiceStatus,
      messages: messages ?? this.messages,
      currentPartialText: clearPartialText ? null : (currentPartialText ?? this.currentPartialText),
      currentSessionId: currentSessionId ?? this.currentSessionId,
      isSpeechRecognitionAvailable: isSpeechRecognitionAvailable ?? this.isSpeechRecognitionAvailable,
      isMicrophoneMuted: isMicrophoneMuted ?? this.isMicrophoneMuted,
      isTTSSpeaking: isTTSSpeaking ?? this.isTTSSpeaking,
      currentSpeakingMessageId: clearSpeakingMessage ? null : (currentSpeakingMessageId ?? this.currentSpeakingMessageId),
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      lastProcessedCommand: lastProcessedCommand ?? this.lastProcessedCommand,
      sessionStartTime: sessionStartTime ?? this.sessionStartTime,
    );
  }

  /// Add a message to the conversation
  ConversationState addMessage(ConversationMessage message) {
    return copyWith(
      messages: [...messages, message],
      clearError: true,
    );
  }

  /// Update a message in the conversation
  ConversationState updateMessage(String messageId, ConversationMessage updatedMessage) {
    final updatedMessages = messages.map((msg) {
      return msg.id == messageId ? updatedMessage : msg;
    }).toList();

    return copyWith(messages: updatedMessages);
  }

  /// Update message status
  ConversationState updateMessageStatus(String messageId, MessageStatus newStatus) {
    final updatedMessages = messages.map((msg) {
      return msg.id == messageId ? msg.copyWithStatus(newStatus) : msg;
    }).toList();

    return copyWith(messages: updatedMessages);
  }

  /// Clear all messages
  ConversationState clearMessages() {
    return copyWith(messages: []);
  }

  /// Get the last message
  ConversationMessage? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  /// Get the last user message
  ConversationMessage? get lastUserMessage {
    return messages.where((msg) => msg.isUser).lastOrNull;
  }

  /// Get the last assistant message
  ConversationMessage? get lastAssistantMessage {
    return messages.where((msg) => msg.isAssistant).lastOrNull;
  }

  /// Check if conversation is active
  bool get isActive => status == ConversationStatus.active;

  /// Check if currently listening
  bool get isListening => voiceStatus == VoiceListeningStatus.listening;

  /// Check if currently paused (waiting for more speech)
  bool get isPaused => voiceStatus == VoiceListeningStatus.paused;

  /// Check if finalizing input
  bool get isFinalizingInput => voiceStatus == VoiceListeningStatus.finalizing;



  /// Check if currently speaking
  bool get isSpeaking => voiceStatus == VoiceListeningStatus.speaking || isTTSSpeaking;

  /// Check if actively in a voice session (listening, paused, or finalizing)
  bool get isInVoiceSession =>
    voiceStatus == VoiceListeningStatus.listening ||
    voiceStatus == VoiceListeningStatus.paused ||
    voiceStatus == VoiceListeningStatus.finalizing;

  /// Check if voice input is available
  bool get canListen => isSpeechRecognitionAvailable && 
                       !isMicrophoneMuted && 
                       voiceStatus != VoiceListeningStatus.listening &&
                       voiceStatus != VoiceListeningStatus.paused &&
                       voiceStatus != VoiceListeningStatus.finalizing &&
                       !isTTSSpeaking;

  /// Check if there's an error state
  bool get hasError => status == ConversationStatus.error || 
                       voiceStatus == VoiceListeningStatus.error ||
                       errorMessage != null;

  /// Get conversation duration
  Duration? get conversationDuration {
    if (sessionStartTime == null) return null;
    return DateTime.now().difference(sessionStartTime!);
  }

  /// Get message count
  int get messageCount => messages.length;

  /// Get user message count
  int get userMessageCount => messages.where((msg) => msg.isUser).length;

  /// Get assistant message count
  int get assistantMessageCount => messages.where((msg) => msg.isAssistant).length;

  /// Check if conversation has any messages
  bool get hasMessages => messages.isNotEmpty;

  /// Get status display text
  String get statusText {
    if (hasError && errorMessage != null) {
      return errorMessage!;
    }
    
    switch (voiceStatus) {
      case VoiceListeningStatus.idle:
        if (!isSpeechRecognitionAvailable) {
          return 'Speech recognition unavailable';
        }
        if (isMicrophoneMuted) {
          return 'Microphone muted';
        }
        return 'Tap to speak';
      case VoiceListeningStatus.listening:
        return 'Listening...';
      case VoiceListeningStatus.paused:
        return 'Paused - continue speaking or wait...';
      case VoiceListeningStatus.finalizing:
        return 'Finalizing your input...';
      case VoiceListeningStatus.speaking:
        return 'Speaking...';
      case VoiceListeningStatus.error:
        return errorMessage ?? 'Voice error occurred';
    }
  }

  /// Get the display text for current input
  String? get currentInputDisplay {
    if (currentPartialText != null && currentPartialText!.isNotEmpty) {
      return currentPartialText;
    }
    if (isInVoiceSession) {
      return 'Say something like: "Buy 10 shares of TCS"';
    }
    return null;
  }

  @override
  List<Object?> get props => [
        status,
        voiceStatus,
        messages,
        currentPartialText,
        currentSessionId,
        isSpeechRecognitionAvailable,
        isMicrophoneMuted,
        isTTSSpeaking,
        currentSpeakingMessageId,
        errorMessage,
        lastProcessedCommand,
        sessionStartTime,
      ];

  @override
  String toString() {
    return 'ConversationState(status: $status, voiceStatus: $voiceStatus, '
           'messageCount: ${messages.length}, hasError: $hasError)';
  }
}

/// Extension to add null-aware lastOrNull
extension IterableExtension<T> on Iterable<T> {
  T? get lastOrNull {
    if (isEmpty) return null;
    return last;
  }
}