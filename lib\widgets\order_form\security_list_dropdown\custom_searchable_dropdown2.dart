import 'dart:async';

import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/services/security_list_search_service.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/order_form/security_list_dropdown/security_list_text_formatter.dart';

import '../../trading_view/trading_view_chart.dart';

//This component is used to list the security list as a drop down list
//this is fully customized
class CustomSearchableDropdown2 extends StatefulWidget {
  final ValueChanged<SecurityModel> onSelected;
  final String type;
  final TextEditingController searchController;
  final ValueNotifier<bool> notifier;
  final Function onClear;

  // 'equity' or 'features'
  final FocusNode focusNode;

  const CustomSearchableDropdown2({
    super.key,
    required this.onSelected,
    required this.type,
    required this.searchController,
    required this.focusNode,
    required this.notifier,
    required this.onClear,
  });

  @override
  _CustomSearchableDropdownState createState() =>
      _CustomSearchableDropdownState();
}

class _CustomSearchableDropdownState extends State<CustomSearchableDropdown2> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  List<SecurityModel> _filteredList = [];
  bool _showDropdown = false;
  // In your widget
  Timer? _debounce;
  final ScrollController _scrollController = ScrollController();

  void onQueryChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      SecurityListSearchService.optimizedSearch(
        query,
        context,
        widget.type,
        //clear
        () {
          setState(() {
            _filteredList = [];
            _showDropdown = false;
          });
          _removeOverlay();
        },
        //setResults
        (results) {
          setState(() {
            _filteredList = results;
            _showDropdown = true;
          });
          _showOverlay();
        },
      );
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    widget.searchController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    // Remove the existing overlay (if any)
    _removeOverlay();

    final overlayState = Overlay.of(context);

    // Create a new overlay with the updated filtered list
    _overlayEntry = OverlayEntry(
      builder: (context) => BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: _removeOverlay,
            child: Stack(
              children: [
                Positioned(
                  width: 220,
                  child: CompositedTransformFollower(
                    link: _layerLink,
                    showWhenUnlinked: false,
                    offset: Offset(0, 50),
                    child: Material(
                      elevation: 4,
                      color: Colors.transparent,
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppTheme.cardColor(themeState.isDarkMode),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppTheme.borderColor(themeState.isDarkMode),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: themeState.isDarkMode ? Colors.black26 : Colors.grey.withOpacity(0.2),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: _filteredList.isEmpty
                            ? Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: Center(
                                  child: Text(
                                    "No results found",
                                    style: TextStyle(
                                      color: AppTheme.textSecondary(themeState.isDarkMode),
                                      fontSize: 15,
                                    ),
                                  ),
                                ),
                              )
                        : ConstrainedBox(
                            constraints: BoxConstraints(maxHeight: 300),
                            child: Scrollbar(
                              controller: _scrollController,
                              thumbVisibility: true,
                              child: ListView.builder(
                                controller: _scrollController,
                                padding: EdgeInsets.zero,
                                shrinkWrap: true,
                                primary: false,
                                itemCount: _filteredList.length,
                                itemBuilder: (context, index) {
                                  final security = _filteredList[index];
                                  return InkWell(
                                    onTap: () {
                                      setState(() {
                                        widget.searchController.text = SecurityListTextFormatter.format(security, widget.type);
                                        _showDropdown = false;
                                      });
                                      widget.onSelected(security);
                                      widget.notifier.value = true;
                                      _removeOverlay();
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 2,
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 32,
                                        decoration: BoxDecoration(
                                          border: BorderDirectional(
                                            bottom: BorderSide(
                                              color: AppTheme.borderColor(themeState.isDarkMode),
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                        child: Center(
                                            child: Row(
                                          children: [
                                            Expanded(
                                              child: SecurityListTextFormatter(
                                                security: security,
                                                type: widget.type,
                                              ),
                                            ),
                                          ],
                                        )),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                  ),
                ),
              ),
            ),
          ],
        ));
        },
      ),
    );

    // Insert the new overlay
    overlayState.insert(_overlayEntry!);
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return CompositedTransformTarget(
          link: _layerLink,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                alignment: Alignment.center,
                height: 48,
                width: 220,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ValueListenableBuilder<bool>(
                  valueListenable: widget.notifier,
                  builder: (context, isValid, child) {
                    return TextFormField(
                      controller: widget.searchController,
                      cursorColor: AppTheme.textSecondary(themeState.isDarkMode),
                      textAlign: TextAlign.start,
                      textAlignVertical: TextAlignVertical.center,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          widget.notifier.value = false;
                          return "Select a stock";
                        }

                        widget.notifier.value = true;
                        return null;
                      },
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontWeight: FontWeight.w400,
                        overflow: TextOverflow.ellipsis,
                      ),
                      decoration: InputDecoration(
                        isDense: true,
                        hintText: 'Search ',
                        hintStyle: TextStyle(
                          fontSize: 16,
                          color: (!isValid) 
                              ? Color(0xffFF4A4A) 
                              : AppTheme.textSecondary(themeState.isDarkMode),
                          fontWeight: FontWeight.w400,
                          overflow: TextOverflow.ellipsis,
                        ),
                        labelStyle: TextStyle(
                          color: (!isValid) 
                              ? Color(0xffFF4A4A) 
                              : AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 16,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: AppTheme.primaryColor(themeState.isDarkMode),
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: AppTheme.borderColor(themeState.isDarkMode),
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: AppTheme.borderColor(themeState.isDarkMode),
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        errorStyle: const TextStyle(
                          fontSize: 0.01,
                        ),
                        errorBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: Colors.transparent),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: AppTheme.primaryColor(themeState.isDarkMode),
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: (!isValid)
                            ? const Color(0xff986561).withOpacity(0.5)
                            : AppTheme.cardColor(themeState.isDarkMode),
                    prefixIcon: widget.searchController.text.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              showDialog(
                                context: context,
                                barrierColor: Colors.transparent,
                                builder: (context) => Dialog.fullscreen(
                                  child: TradingViewChart(
                                    symbol: widget.searchController.text,
                                    exchange:
                                        widget.type == 'equity' ? 'BSE' : 'NFO',
                                    zenId: widget
                                            .searchController.text.isNotEmpty
                                        ? _filteredList
                                            .firstWhere((security) =>
                                                security.tradingSymbol ==
                                                widget.searchController.text)
                                            .zenId
                                        : null,
                                  ),
                                ),
                              );
                            },
                            icon: Icon(
                              Icons.show_chart,
                              color: AppTheme.textSecondary(themeState.isDarkMode),
                              size: 20,
                            ),
                          )
                        : null,
                    suffixIcon: (!isValid)
                        ? Padding(
                            padding: const EdgeInsets.all(3.0),
                            child: ImageIcon(
                              AssetImage("images/warning-icon.png"),
                              color: Color.fromARGB(255, 255, 41, 41),
                              size: 10,
                            ),
                          )
                        : widget.searchController.text.isEmpty
                            ? null
                            : IconButton(
                                ///Clearing the data here
                                onPressed: () {
                                  widget.searchController.clear();
                                  widget.onClear();
                                  BlocProvider.of<WebSocketBloc>(context)
                                      .add(WebSocketSelectStock(null));
                                  _removeOverlay();
                                },
                                icon: Icon(
                                  Icons.clear,
                                  color: AppTheme.textSecondary(themeState.isDarkMode),
                                  size: 20,
                                ),
                              ),
                    suffixIconConstraints: BoxConstraints(
                      minHeight: 25,
                      minWidth: 25,
                    ),
                  ),
                  onChanged: onQueryChanged,
                  focusNode: widget.focusNode,
                  onTapOutside: (PointerDownEvent event) {
                    FocusManager.instance.primaryFocus?.unfocus();
                    widget.focusNode.unfocus();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
      },
    );
  }
}
