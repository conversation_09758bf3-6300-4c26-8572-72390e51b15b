import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/bottom_navigation/bloc/bottom_navigation_bloc.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/watchlist/bloc/watchist_bloc.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/screens/orders/orders_screen.dart';
import 'package:phoenix/screens/spider/spider_page.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/screens/portfolio/portfolio_screen.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/app_bar/custom_app_bar.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/home/<USER>';
import 'package:phoenix/widgets/toast/custom_toast.dart';
import 'package:zen_navbar/zen_navbar.dart';
import 'features/orders_state/bloc/orders_state_bloc.dart';
import 'features/pnl/bloc/pnl_bloc.dart';
import 'features/portfolio_data/bloc/portfolio_bloc.dart';
import 'widgets/animated_order_button.dart';
import 'widgets/strategy_fab.dart';
import 'package:phoenix/widgets/app_bar/voice_appbar_button.dart';
import 'package:phoenix/widgets/voice_command_handler.dart';
import 'package:phoenix/screens/watchlist/watchlist_screen.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

//this is home with app bar, side menu, bottom nav bar center button
class _HomeState extends State<Home> with TickerProviderStateMixin {
  late final AnimationController _formSheetAnimeController;

  @override
  void initState() {
    super.initState();
    _formSheetAnimeController = BottomSheet.createAnimationController(this);
    _formSheetAnimeController.duration = const Duration(milliseconds: 850);
  }

  final List<Widget> screens = const [
    PortfolioScreen(),
    WatchlistScreen(),
    OrdersScreen(),
    SpiderPage(),
  ];

  Future<bool> _onWillPop(BuildContext context, int currentIndex) async {
    if (currentIndex != 0) {
      context.read<BottomNavigationBloc>().add(
            BottomNavigationChangeEvent(currentPageIndex: 0),
          );
      return false;
    } else {
      return await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text("Exit App"),
              content: const Text("Are you sure you want to exit the app?"),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text("No"),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text("Yes"),
                ),
              ],
            ),
          ) ??
          false;
    }
  }

  void _handleOrderState(
    BuildContext context,
    OrdersState state,
  ) {
    if (state is OrderPlaced) {
      if (state.status == "ACCEPTED") {
        if (state.methodType == "PUT") {
          ZenSnackbar.show(
            context,
            'Modify in progress',
            type: ToastType.info,
          );
        } else if (state.methodType == "DELETE") {
          ZenSnackbar.show(
            context,
            'Delete in progress',
            type: ToastType.info,
          );
        } else {
          ZenSnackbar.show(
            context,
            'Accepted',
            type: ToastType.success,
          );
        }
      } else if (state.status == "REJECTED") {
        if (state.methodType == "PUT") {
          ZenSnackbar.show(
            context,
            'Modifiy order rejected',
            type: ToastType.error,
          );
        } else if (state.methodType == "DELETE") {
          ZenSnackbar.show(
            context,
            'Delete in progress',
            type: ToastType.error,
          );
        } else {
          ZenSnackbar.show(
            context,
            state.status.toCapitalized,
            type: ToastType.error,
          );
        }
      } else {
        ZenSnackbar.show(
          context,
          state.status.toCapitalized,
          type: ToastType.warning,
        );
      }

      final authState = context.read<AuthBloc>().state; // Get current state
      if (authState is AuthAuthenticated) {
        BlocProvider.of<OrdersStateBloc>(context).add(FetchOrdersState(
          clientId: authState.credentialsModel.clientId,
        ));
      } else {
        // Handle unauthenticated case (e.g., show login dialog)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("You need to log in first")),
        );
      }
      //Resetting to initial event
      BlocProvider.of<OrdersBloc>(context).add(OrdersInitializeEvent());
    }

    // OrderLoading state - let the order form handle its own closing
    // Removed automatic modal closing to prevent interference with navigation

    if (state is OrderError) {
      ZenSnackbar.show(
        context,
        state.message.toCapitalized,
        type: ToastType.error,
      );

      //Resetting to initial event
      BlocProvider.of<OrdersBloc>(context).add(OrdersInitializeEvent());
    }
  }

  // State variable to track if the buy/sell buttons are visible
  bool _isBuySellVisible = false;

  void _toggleBuySellButtons() {
    setState(() {
      _isBuySellVisible = !_isBuySellVisible;
      print('Center button tapped. _isBuySellVisible: $_isBuySellVisible');
    });
  }

  Future<void> _showOrderForm(FormOpenOrderType type, int pageIndex) async {
    setState(() {
      _isBuySellVisible = false;
    });

    // Store the current context's mounted status
    final isStillMounted = mounted;

    await Future.delayed(const Duration(milliseconds: 300));

    if (!isStillMounted) return;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      transitionAnimationController: _formSheetAnimeController,
      builder: (modalContext) {
        return SingleChildScrollView(
          reverse: true,
          child: OrderFormSheet(openOrderType: type),
        );
      },
    ).whenComplete(() {
      debugPrint("$type form closed");

      if (mounted) {
        // Only access context when mounted
        BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));

        if (pageIndex == 0) {
          handlePositionPnlSocketSubscriptions(context);
        } else if (pageIndex == 1) {
          handleWatchlistPageSocketSubscriptions(context);
        } else if (pageIndex == 2) {
          handleOrdersPageSocketSubscriptions(context);
        }
      }
    }).catchError((error) {
      debugPrint("Error in $type modal: $error");
    });
  }

  void handlePositionPnlSocketSubscriptions(BuildContext context) {
    final Set<int> zenSecIds = {};

    // From PnL Bloc
    final pnlState = context.read<PnlBloc>().state;
    if (pnlState is PnlLoaded) {
      zenSecIds.addAll(
        pnlState.pnlData.map((p) => p.positionCompositeKey.zenSecId),
      );
    }

    // From Portfolio Bloc
    final portfolioState = context.read<PortfolioBloc>().state;
    if (portfolioState is PortfolioLoaded) {
      zenSecIds.addAll(
        portfolioState.openPositions
            .map((p) => p.positionCompositeKey.zenSecId),
      );
      zenSecIds.addAll(
        portfolioState.closedPositions
            .map((p) => p.positionCompositeKey.zenSecId),
      );
    }

    debugPrint(
      "🔄 Updating WebSocket subscriptions for position & pnl - ${zenSecIds.length} unique stocks",
    );

    context.read<WebSocketBloc>().add(
          WebSocketSelectMultipleStocks(zenSecIds.toList()),
        );
  }

  void handleOrdersPageSocketSubscriptions(BuildContext context) {
    final ordersState = context.read<OrdersStateBloc>().state;
    if (ordersState is OrdersStateLoaded) {
      final openData = ordersState.data
          .where((order) => [
                "update",
                "trigger_pending",
                "open",
                "in_progress",
                "pending"
              ].contains(order.status.toLowerCase()))
          .toList();
      Set<int> stockIds =
          openData.map((e) => e.positionCompKey.zenSecId).toSet();
      debugPrint(
        "🔄 Updating WebSocket subscriptions for open orders - ${stockIds.length} stocks",
      );
      context
          .read<WebSocketBloc>()
          .add(WebSocketSelectMultipleStocks(stockIds.toList()));
    }
  }

  void handleWatchlistPageSocketSubscriptions(BuildContext context) {
    final wl = context.read<WatchistBloc>().state;
    if (wl is WatchlistLoaded) {
      final stockIds =
          wl.watchlistData[0].securities.map((wl) => wl.zenId).toSet().toList();
      debugPrint(
        "🔄 Updating WebSocket subscriptions for WL - ${stockIds.length} stocks",
      );
      context
          .read<WebSocketBloc>()
          .add(WebSocketSelectMultipleStocks(stockIds));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(builder: (context, themeState) {
      return BlocBuilder<SecurityListBloc, SecurityListState>(
        builder: (context, _) {
          return BlocBuilder<BottomNavigationBloc, BottomNavigationState>(
            builder: (context, navState) {
              final currentIndex = (navState is BottomNavigaionChanged)
                  ? navState.currentPageIndex
                  : 0;

              final bool isSpiderScreen = currentIndex == 3;

              final List<NavItem> navItems = [
                NavItem(
                  icon: "images/nav-bar/portfolio_icon.png",
                  label: "Portfolio",
                  screen: screens[0],
                ),
                NavItem(
                  icon:
                      "images/nav-bar/watchlist_icon.png", // Update this icon as needed
                  label: "Watchlist",
                  screen: screens[1],
                ),
                NavItem(
                  icon: "images/nav-bar/orders_icon.png",
                  label: "Orders",
                  screen: screens[2],
                ),
                NavItem(
                  icon: "images/nav-bar/graph_icon.png",
                  label: "Spider",
                  screen: screens[3],
                ),
              ];

              ///This is to listen to the order state and show the toast message
              return BlocConsumer<OrdersBloc, OrdersState>(
                listener: _handleOrderState,
                builder: (context, state) {
                  return VoiceCommandHandler(
                    child: WillPopScope(
                      onWillPop: () => _onWillPop(context, currentIndex),
                      child: Stack(
                        children: [
                          Scaffold(
                          backgroundColor: isSpiderScreen
                              ? Colors.transparent
                              : AppTheme.backgroundColor(themeState.isDarkMode),
                          drawer: const SideDrawer(),
                          appBar: isSpiderScreen
                              ? null
                              : CustomAppBar(
                                  onClientSwitched: (clientId) {
                                    switch (currentIndex) {
                                      case 0:
                                        context
                                            .read<PortfolioBloc>()
                                            .add(FetchPortfolio(clientId));
                                        context
                                            .read<PnlBloc>()
                                            .add(FetchPnlData(clientId));
                                        break;
                                      case 1:
                                        context
                                            .read<WatchistBloc>()
                                            .add(WatchlistData(clientId));
                                        break;
                                      case 2:
                                        context.read<OrdersStateBloc>().add(
                                            FetchOrdersState(
                                                clientId: clientId));
                                        break;
                                      case 3:
                                        break;
                                    }
                                  },
                                  actions: const [VoiceCommandAppBarButton()],
                                ),
                          body: navItems[currentIndex].screen,
                          bottomNavigationBar: ZenNavBar(
                            items: navItems,
                            backgroundColor:
                                AppTheme.backgroundColor(themeState.isDarkMode),
                            selectedItemColor:
                                AppTheme.primaryColor(themeState.isDarkMode),
                            unselectedItemColor:
                                AppTheme.textPrimary(themeState.isDarkMode),
                            centerButton: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 700),
                              transitionBuilder: (child, animation) =>
                                  FadeTransition(
                                opacity: animation,
                                child: ScaleTransition(
                                    scale: animation, child: child),
                              ),
                              child: Container(
                                key: ValueKey(_isBuySellVisible),
                                height: 60,
                                width: 60,
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0xFF2D6FE6)
                                          .withOpacity(0.4),
                                      blurRadius: 15,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                  image: DecorationImage(
                                    image: AssetImage(_isBuySellVisible
                                        ? 'images/floating-cross-button.png'
                                        : 'images/plus_button.png'),
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                            onCenterButtonTap: () => _showBuySellDialog(currentIndex),
                            onItemTap: (int index) {
                              context.read<BottomNavigationBloc>().add(
                                  BottomNavigationChangeEvent(
                                      currentPageIndex: index));
                            },
                          ),
                          floatingActionButton: (!isSpiderScreen)
                              ? BlocBuilder<BottomNavigationBloc, BottomNavigationState>(
                                  builder: (context, state) {
                                    final isNetWorthBarExpanded =
                                        (state is BottomNavigaionChanged)
                                            ? state.isNetWorthBarExpanded
                                            : false;
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        // Strategy FAB
                                        Padding(
                                          padding: const EdgeInsets.only(right: 4),
                                          child: StrategyFAB(
                                            isNetWorthBarExpanded: isNetWorthBarExpanded,
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                )
                              : null,
                        ),
                        if (state is OrderLoading)
                          Positioned.fill(
                            child: Container(
                              color: AppTheme.backgroundColor(themeState.isDarkMode),
                              child: Center(
                                child: CircularLoader(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      );
    });
  }

  // Modern Buy/Sell dialog with theme support
  Future<void> _showBuySellDialog(int currentIndex) async {
    final result = await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            final isDarkMode = themeState.isDarkMode;
            
            return Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.cardColor(isDarkMode),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(isDarkMode ? 0.3 : 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    padding: EdgeInsets.fromLTRB(
                      24, 
                      32, 
                      24, 
                      32 + MediaQuery.of(context).viewInsets.bottom
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.cardColor(isDarkMode).withOpacity(0.9),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppTheme.borderColor(isDarkMode),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Handle bar
                        Container(
                          width: 50,
                          height: 4,
                          decoration: BoxDecoration(
                            color: AppTheme.textSecondary(isDarkMode),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(height: 24),
                        
                        // Title with icon
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.trending_up,
                              color: AppTheme.primaryColor(isDarkMode),
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Choose Action',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimary(isDarkMode),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Select whether you want to buy or sell',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textSecondary(isDarkMode),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),
                        
                        // Action buttons
                        Row(
                          children: [
                            // Buy Button
                            Expanded(
                              child: GestureDetector(
                                onTap: () => Navigator.of(context).pop('buy'),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 20),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        ThemeConstants.buySlideOptionColor,
                                        ThemeConstants.buySlideOptionColor.withOpacity(0.8),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: ThemeConstants.buySlideOptionColor.withOpacity(0.3),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.trending_up,
                                        color: Colors.white,
                                        size: 32,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'BUY',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          letterSpacing: 1.2,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Long Position',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.white.withOpacity(0.8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            
                            // Sell Button
                            Expanded(
                              child: GestureDetector(
                                onTap: () => Navigator.of(context).pop('sell'),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 20),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        ThemeConstants.sellSlideOptionColor,
                                        ThemeConstants.sellSlideOptionColor.withOpacity(0.8),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: ThemeConstants.sellSlideOptionColor.withOpacity(0.3),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.trending_down,
                                        color: Colors.white,
                                        size: 32,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'SELL',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          letterSpacing: 1.2,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Short Position',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.white.withOpacity(0.8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // Cancel button
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32, 
                              vertical: 12
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              fontSize: 16,
                              color: AppTheme.textSecondary(isDarkMode),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
    
    if (result == 'buy') {
      _showOrderForm(FormOpenOrderType.buy, currentIndex);
    } else if (result == 'sell') {
      _showOrderForm(FormOpenOrderType.sell, currentIndex);
    }
  }
}
