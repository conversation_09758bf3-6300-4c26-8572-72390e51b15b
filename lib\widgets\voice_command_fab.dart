import 'package:flutter/material.dart';
import 'package:phoenix/services/voice_testing_service.dart';
import 'package:phoenix/widgets/voice_testing_widget.dart';

/// Floating Action Button for quick voice command testing
class VoiceCommandFAB extends StatefulWidget {
  const VoiceCommandFAB({Key? key}) : super(key: key);

  @override
  State<VoiceCommandFAB> createState() => _VoiceCommandFABState();
}

class _VoiceCommandFABState extends State<VoiceCommandFAB>
    with SingleTickerProviderStateMixin {
  final VoiceTestingService _testingService = VoiceTestingService();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _showVoiceTestingDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: const VoiceTestingWidget(),
        ),
      ),
    );
  }

  Future<void> _quickTest(String command) async {
    await _testingService.testVoiceCommand(command);
    _toggleExpanded(); // Close the menu after testing
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Quick test buttons
        if (_isExpanded) ...[
          ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              children: [
                _buildQuickTestButton(
                  'Buy TCS',
                  'buy 1 shares of Apple stock',
                  Icons.trending_up,
                  Colors.green,
                ),
                const SizedBox(height: 8),
                _buildQuickTestButton(
                  'Sell TSLA',
                  'sell 5 shares of Tesla stock',
                  Icons.trending_down,
                  Colors.red,
                ),
                const SizedBox(height: 8),
                _buildQuickTestButton(
                  'Portfolio',
                  'Check my portfolio',
                  Icons.pie_chart,
                  Colors.blue,
                ),
                const SizedBox(height: 8),
                _buildQuickTestButton(
                  'Quote',
                  'get TCS stock price',
                  Icons.attach_money,
                  Colors.orange,
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
        
        // Main FAB
        FloatingActionButton(
          onPressed: _toggleExpanded,
          backgroundColor: _isExpanded ? Colors.grey : Colors.blue,
          child: AnimatedRotation(
            turns: _isExpanded ? 0.125 : 0.0, // 45 degrees when expanded
            duration: const Duration(milliseconds: 300),
            child: Icon(_isExpanded ? Icons.close : Icons.mic),
          ),
        ),
        
        // Testing dialog button
        if (_isExpanded)
          ScaleTransition(
            scale: _scaleAnimation,
            child: Padding(
              padding: const EdgeInsets.only(top: 8),
              child: FloatingActionButton.small(
                onPressed: _showVoiceTestingDialog,
                backgroundColor: Colors.purple,
                child: const Icon(Icons.settings_voice, size: 16),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildQuickTestButton(
    String label,
    String command,
    IconData icon,
    Color color,
  ) {
    return FloatingActionButton.extended(
      onPressed: () => _quickTest(command),
      backgroundColor: color,
      icon: Icon(icon, size: 16),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      heroTag: command, // Unique hero tag for each button
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}