import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/features/pnl_graph/bloc/pnl_graph_bloc.dart';
import 'package:phoenix/features/pnl_graph/repository/pnl_graph_formatter.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/screens/pnl/inner_screen/pnl_graph_container.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/pnl/pnl_type_toggler.dart';

import '../../../widgets/custom/dismissible/inner_shadow_gradient.dart';

class PnlDetailPage extends StatefulWidget {
  final PositionPnL data;
  final BrokerAccountStrategyData brokerAccountStrategyData;

  const PnlDetailPage({
    super.key,
    required this.data,
    required this.brokerAccountStrategyData,
  });

  @override
  State<PnlDetailPage> createState() => _PnlDetailPageState();
}

class _PnlDetailPageState extends State<PnlDetailPage> {
  PnlViewState _currentState = PnlViewState.unrealized;
  PnLPeriodType _currentPeriod = PnLPeriodType.ltd;

  late double unRealizedLTD;
  late double unRealizedPercentageLTD;

  late double realizedLTD;
  late double realizedPercentageLTD;

  late double unRealizedDTD;
  late double unRealizedPercentageDTD;

  late double realizedDTD;
  late double realizedPercentageDTD;

  late double unRealizedWTD;
  late double unRealizedPercentageWTD;

  late double realizedWTD;
  late double realizedPercentageWTD;

  late double unRealizedMTD;
  late double unRealizedPercentageMTD;

  late double realizedMTD;
  late double realizedPercentageMTD;

  late double unRealizedYTD;
  late double unRealizedPercentageYTD;

  late double realizedYTD;
  late double realizedPercentageYTD;

  void pnlCalculations() {
    debugPrint("PNL Calculations");

    final openCost = widget.data.openCost;
    final unRealized = widget.data.unRealized;
    final realized = widget.data.realized;

    double safePercent(double value, double base) {
      if (base == 0) return 0;
      return (value / base) * 100;
    }

    // LTD
    unRealizedLTD = unRealized.latest;
    unRealizedPercentageLTD = safePercent(unRealizedLTD, openCost);

    realizedLTD = realized.latest;
    realizedPercentageLTD = safePercent(realizedLTD, openCost);

    // DTD
    unRealizedDTD = unRealized.latest - unRealized.sod;
    unRealizedPercentageDTD = safePercent(unRealizedDTD, openCost);

    realizedDTD = realized.latest - realized.sod;
    realizedPercentageDTD = safePercent(realizedDTD, openCost);

    // WTD
    unRealizedWTD = unRealized.latest - unRealized.sow;
    unRealizedPercentageWTD = safePercent(unRealizedWTD, openCost);

    realizedWTD = realized.latest - realized.sow;
    realizedPercentageWTD = safePercent(realizedWTD, openCost);

    // MTD
    unRealizedMTD = unRealized.latest - unRealized.som;
    unRealizedPercentageMTD = safePercent(unRealizedMTD, openCost);

    realizedMTD = realized.latest - realized.som;
    realizedPercentageMTD = safePercent(realizedMTD, openCost);

    // YTD
    unRealizedYTD = unRealized.latest - unRealized.soy;
    unRealizedPercentageYTD = safePercent(unRealizedYTD, openCost);

    realizedYTD = realized.latest - realized.soy;
    realizedPercentageYTD = safePercent(realizedYTD, openCost);
  }

  @override
  void initState() {
    super.initState();

    pnlCalculations();
  }

  @override
  void dispose() {
    super.dispose();
    debugPrint("PNL Detail Page Disposed");
    context.read<PnlGraphBloc>().add(ClearPnlGraphData());
  }

  @override
  Widget build(BuildContext context) {
    bool showUnrealized = _currentState == PnlViewState.unrealized ||
        _currentState == PnlViewState.both;
    bool showRealized = _currentState == PnlViewState.realized ||
        _currentState == PnlViewState.both;

    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Stack(
          children: [
            const GradientWithBoxInnerShadow(),
            Opacity(
              opacity: 0.8,
              child: Container(
                color: AppTheme.backgroundColor(themeState.isDarkMode),
              ),
            ),
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: Icon(Icons.arrow_back, 
                    color: AppTheme.textPrimary(themeState.isDarkMode)),
                  onPressed: () => Navigator.pop(context),
                ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.data.tradingSymbol,
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      widget.brokerAccountStrategyData.brokerName,
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 3),
                    Text(
                      " | ",
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 3),
                    Text(
                      widget.brokerAccountStrategyData.strategyName,
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          body: SingleChildScrollView(
            child: AnimatedSize(
              duration: const Duration(milliseconds: 350),
              curve: Curves.easeInOut,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // LTD
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            pnlCardOnClick(PnLPeriodType.ltd);
                          },
                          child: _buildPNLCard(
                            title: 'LTD',
                            showUnrealized: showUnrealized,
                            showRealized: showRealized,
                            realized: realizedLTD,
                            unrealized: unRealizedLTD,
                            unrealizedPercentage: unRealizedPercentageLTD,
                            realizedPercentage: realizedPercentageLTD,
                            periodType: PnLPeriodType.ltd,
                            valuefontSize: 24,
                          ),
                        ),

                        //
                        //Pnl type Realized / Unrealized / Both
                        //
                        PnlTypeToggler(
                          onToggle: (state) {
                            // Handle the state change
                            _currentState = state;
                            setState(() {});
                            context.read<PnlGraphBloc>().add(
                                  FormatPnlGraphData(
                                    type: PnLPeriodType.ltd,
                                    isRealized: _currentState ==
                                            PnlViewState.both ||
                                        _currentState == PnlViewState.realized,
                                    isUnrealized:
                                        _currentState == PnlViewState.both ||
                                            _currentState ==
                                                PnlViewState.unrealized,
                                  ),
                                );
                          },
                        ),
                      ],
                    ),
                  ),

                  // PNL Grid
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            //DTD
                            Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  pnlCardOnClick(PnLPeriodType.dtd);
                                },
                                child: _buildPNLCard(
                                  title: 'DTD',
                                  showUnrealized: showUnrealized,
                                  showRealized: showRealized,
                                  realized: realizedDTD,
                                  unrealized: unRealizedDTD,
                                  unrealizedPercentage: unRealizedPercentageDTD,
                                  realizedPercentage: realizedPercentageDTD,
                                  periodType: PnLPeriodType.dtd,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            //WTD
                            Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  pnlCardOnClick(PnLPeriodType.wtd);
                                },
                                child: _buildPNLCard(
                                  title: 'WTD',
                                  showUnrealized: showUnrealized,
                                  showRealized: showRealized,
                                  realized: realizedWTD,
                                  unrealized: unRealizedWTD,
                                  unrealizedPercentage: unRealizedPercentageWTD,
                                  realizedPercentage: realizedPercentageWTD,
                                  periodType: PnLPeriodType.wtd,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  pnlCardOnClick(PnLPeriodType.mtd);
                                },
                                child: _buildPNLCard(
                                  title: 'MTD',
                                  showUnrealized: showUnrealized,
                                  showRealized: showRealized,
                                  realized: realizedMTD,
                                  unrealized: unRealizedMTD,
                                  unrealizedPercentage: unRealizedPercentageMTD,
                                  realizedPercentage: realizedPercentageMTD,
                                  periodType: PnLPeriodType.mtd,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  pnlCardOnClick(PnLPeriodType.ytd);
                                },
                                child: _buildPNLCard(
                                  title: 'YTD',
                                  showUnrealized: showUnrealized,
                                  showRealized: showRealized,
                                  realized: realizedYTD,
                                  unrealized: unRealizedYTD,
                                  unrealizedPercentage: unRealizedPercentageYTD,
                                  realizedPercentage: realizedPercentageYTD,
                                  periodType: PnLPeriodType.ytd,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  //Line chart
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 2, vertical: 6),
                    child: PnlGraphContainer(
                      data: widget.data,
                      isRealized: _currentState == PnlViewState.both ||
                          _currentState == PnlViewState.realized,
                      isUnrealized: _currentState == PnlViewState.both ||
                          _currentState == PnlViewState.unrealized,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Investment Summary
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 2, vertical: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildSummaryItem(
                          'Invested',
                          UtilFunctions.formatIndianCurrency(
                            widget.data.openCost,
                          ),
                        ),
                        _buildSummaryItem('Current', "--"),
                        _buildSummaryItem(
                          'Quantity',
                          widget.data.position.toString(),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Dividend Info Card
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildInfoCard(
                      'Dividend info',
                      [
                        _buildInfoRow(
                          'DTD',
                          widget.data.dividend.latest,
                          'WTD',
                          (widget.data.dividend.latest -
                              widget.data.dividend.sow),
                        ),
                        _buildInfoRow(
                          'MTD',
                          (widget.data.dividend.latest -
                              widget.data.dividend.som),
                          'YTD',
                          (widget.data.dividend.latest -
                              widget.data.dividend.soy),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Charges Info Card
                  if (widget.data.charges != null) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildInfoCard(
                      'Charges info',
                      [
                        _buildInfoRow(
                          'DTD',
                          widget.data.charges!.latest,
                          'WTD',
                          (widget.data.charges!.latest - widget.data.charges!.sow ),
                        ),
                        _buildInfoRow(
                          'MTD',
                          (widget.data.charges!.latest - widget.data.charges!.som),
                          'YTD',
                          (widget.data.charges!.latest - widget.data.charges!.soy),
                        ),
                      ],
                    ),
                  ),
                  ],
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
          ],
        );
      },
    );
  }



  void pnlCardOnClick(PnLPeriodType type) {
    context.read<PnlGraphBloc>().add(
          FormatPnlGraphData(
            type: type,
            isRealized: _currentState == PnlViewState.both ||
                _currentState == PnlViewState.realized,
            isUnrealized: _currentState == PnlViewState.both ||
                _currentState == PnlViewState.unrealized,
          ),
        );
    _currentPeriod = type;
    setState(() {});
  }

  Widget _buildPNLCard({
    required String title,
    required double unrealized,
    required double realized,
    required double unrealizedPercentage,
    required double realizedPercentage,
    required bool showUnrealized,
    required bool showRealized,
    required PnLPeriodType periodType,
    double valuefontSize = 18,
  }) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        String formatValue(double value) =>
            value == 0 ? '--' : UtilFunctions.formatIndianCurrencyforPnl(value);

        Color getValueColor(double value) {
          if (value == 0) return AppTheme.textSecondary(themeState.isDarkMode);
          return value > 0
              ? AppTheme.tileGreenColor
              : AppTheme.titleRedColor;
        }

        final Color unRealizedColor = getValueColor(unrealized);
        final Color realizedColor = getValueColor(realized);
        final Color unRealizedPercentageColor = getValueColor(unrealizedPercentage);
        final Color realizedPercentageColor = getValueColor(realizedPercentage);

        final bool isRealizedZero = realized == 0;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (_currentPeriod == periodType) SelectedLable()
              ],
            ),
        const SizedBox(height: 8),
        if (showUnrealized)
          Row(
            children: [
              const SizedBox(width: 4),
              Text(
                formatValue(unrealized),
                style: TextStyle(
                  color: unRealizedColor,
                  fontSize: valuefontSize,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                unrealizedPercentage == 0
                    ? '--'
                    : "${unrealizedPercentage.toStringAsFixed(2)}%",
                style: TextStyle(
                  color: unRealizedPercentageColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        const SizedBox(height: 8),
        if (showRealized)
          Row(
            children: [
              if (!isRealizedZero)
                Icon(
                  realized >= 0 ? Icons.trending_up : Icons.trending_down,
                  color: realized == 0
                      ? AppTheme.textSecondary(themeState.isDarkMode)
                      : (realized > 0
                          ? AppTheme.tileGreenColor
                          : AppTheme.titleRedColor),
                  size: 18,
                ),
              const SizedBox(width: 4),
              Text(
                formatValue(realized),
                style: TextStyle(
                  color: realizedColor,
                  fontSize: valuefontSize,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                realizedPercentage == 0
                    ? '--'
                    : "${realizedPercentage.toStringAsFixed(2)}%",
                style: TextStyle(
                  color: realizedPercentageColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          ],
        );
      },
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Column(
          children: [
            Text(
              label,
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(12),
            boxShadow: ThemeConstants.getNeomorpicShadow(themeState.isDarkMode)
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              ...children,
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(
    String label1,
    double value1,
    String label2,
    double value2,
  ) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        Color value1Color = AppTheme.textSecondary(themeState.isDarkMode);
        Color value2Color = AppTheme.textSecondary(themeState.isDarkMode);

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label1,
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 15,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      UtilFunctions.formatIndianCurrency(value1),
                      style: TextStyle(
                        color: value1Color,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label2,
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 15,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      UtilFunctions.formatIndianCurrency(value2),
                      style: TextStyle(
                        color: value2Color,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class SelectedLable extends StatelessWidget {
  const SelectedLable({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          margin: const EdgeInsets.only(left: 8),
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Selected',
            style: TextStyle(
              color: AppTheme.primaryColor(themeState.isDarkMode),
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      },
    );
  }
}
