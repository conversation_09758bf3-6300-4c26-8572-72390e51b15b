import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';

class RenameWatchlistDialog extends StatefulWidget {
  final String initialName;
  final Future<void> Function(String newName) onRename;

  const RenameWatchlistDialog({
    super.key,
    required this.initialName,
    required this.onRename,
  });

  @override
  State<RenameWatchlistDialog> createState() => _RenameWatchlistDialogState();
}

class _RenameWatchlistDialogState extends State<RenameWatchlistDialog> {
  late TextEditingController _renameController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _renameController = TextEditingController(text: widget.initialName);
  }

  @override
  void dispose() {
    _renameController.dispose();
    super.dispose();
  }

  Future<void> _handleRename() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onRename(_renameController.text.trim());
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Handle error if needed
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final isDarkMode = themeState.isDarkMode;
        return AlertDialog(
          backgroundColor: ThemeConstants.getSurfaceColor(isDarkMode),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Rename Watchlist',
            style: TextStyle(
              color: ThemeConstants.getTextColor(isDarkMode),
              fontWeight: FontWeight.w500,
            ),
          ),
          content: Container(
            decoration: BoxDecoration(
              color: ThemeConstants.getBackgroundColor(isDarkMode),
              borderRadius: BorderRadius.circular(8),
              boxShadow: ThemeConstants.getNeomorpicShadow(isDarkMode),
            ),
            child: TextField(
              controller: _renameController,
              style: TextStyle(color: ThemeConstants.getTextColor(isDarkMode)),
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                hintText: 'Enter new name',
                hintStyle: TextStyle(color: isDarkMode ? Colors.grey : Colors.black45),
                border: InputBorder.none,
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: ThemeConstants.blue),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeConstants.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: _isLoading ? null : _handleRename,
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text(
                      'Save',
                      style: TextStyle(color: Colors.white),
                    ),
            ),
          ],
        );
      },
    );
  }
}