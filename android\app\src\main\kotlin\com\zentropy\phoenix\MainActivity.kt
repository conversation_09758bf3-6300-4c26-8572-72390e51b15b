package com.zentropytech.phoenix

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.GeneratedPluginRegistrant
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterFragmentActivity() {
    private val VOICE_CHANNEL = "com.phoenix.voice_assistant"
    private var methodChannel: MethodChannel? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        
        // Set up method channel for voice assistant communication
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, VOICE_CHANNEL)
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "handleVoiceCommand" -> {
                    val command = call.argument<String>("command")
                    val parameters = call.argument<Map<String, Any>>("parameters")
                    handleVoiceCommand(command, parameters)
                    result.success(true)
                }
                else -> result.notImplemented()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        intent?.let {
            when (it.action) {
                "com.phoenix.BUY_STOCK" -> handleAssistantAction("buy", it)
                "com.phoenix.SELL_STOCK" -> handleAssistantAction("sell", it)
                "com.phoenix.CHECK_PORTFOLIO" -> handleAssistantAction("portfolio", it)
                "com.phoenix.GET_QUOTE" -> handleAssistantAction("quote", it)
                Intent.ACTION_VIEW -> handleDeepLink(it)
            }
        }
    }

    private fun handleAssistantAction(action: String, intent: Intent) {
        val parameters = mutableMapOf<String, Any>()
        parameters["action"] = action
        
        // Extract parameters from intent extras
        intent.extras?.let { extras ->
            for (key in extras.keySet()) {
                extras.get(key)?.let { value ->
                    parameters[key] = value
                }
            }
        }

        // Send to Flutter
        methodChannel?.invokeMethod("onAssistantIntent", mapOf(
            "action" to action,
            "parameters" to parameters
        ))
    }

    private fun handleDeepLink(intent: Intent) {
        val uri = intent.data
        if (uri != null && uri.scheme == "phoenix" && uri.host == "voice") {
            val parameters = mutableMapOf<String, Any>()
            parameters["uri"] = uri.toString()
            
            // Extract query parameters
            for (paramName in uri.queryParameterNames) {
                uri.getQueryParameter(paramName)?.let { value ->
                    parameters[paramName] = value
                }
            }

            // Send to Flutter
            methodChannel?.invokeMethod("onDeepLink", mapOf(
                "uri" to uri.toString(),
                "parameters" to parameters
            ))
        }
    }

    private fun handleVoiceCommand(command: String?, parameters: Map<String, Any>?) {
        // Handle voice commands from Flutter side if needed
        // This can be used for additional native processing
    }
}
