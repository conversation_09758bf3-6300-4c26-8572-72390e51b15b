import 'package:flutter/material.dart';
import 'package:phoenix/widgets/voice_testing_widget.dart';

/// Screen dedicated to testing voice commands
class VoiceTestScreen extends StatelessWidget {
  const VoiceTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Command Testing'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Voice Command Testing',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Use this screen to test voice commands during development. '
              'This simulates what Google Assistant would send to your app.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 24),
            VoiceTestingWidget(),
          ],
        ),
      ),
    );
  }
}