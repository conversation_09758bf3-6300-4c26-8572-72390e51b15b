import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:phoenix/services/voice_assistant_service.dart';

/// Service to handle method channel communication for voice assistant
class VoiceMethodChannelService {
  static const MethodChannel _channel = MethodChannel('com.phoenix.voice_assistant');
  static final VoiceMethodChannelService _instance = VoiceMethodChannelService._internal();
  factory VoiceMethodChannelService() => _instance;
  VoiceMethodChannelService._internal();

  final VoiceAssistantService _voiceAssistant = VoiceAssistantService();
  bool _isInitialized = false;

  /// Initialize the method channel service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Set up method call handler
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // Initialize voice assistant service
      await _voiceAssistant.initialize();
      
      _isInitialized = true;
      debugPrint('Voice Method Channel: Initialized successfully');
    } catch (e) {
      debugPrint('Voice Method Channel: Initialization failed - $e');
    }
  }

  /// Handle method calls from native Android code
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    try {
      debugPrint('Voice Method Channel: Received call - ${call.method}');
      
      switch (call.method) {
        case 'onAssistantIntent':
          return await _handleAssistantIntent(call.arguments);
        
        case 'onDeepLink':
          return await _handleDeepLink(call.arguments);
        
        default:
          debugPrint('Voice Method Channel: Unknown method - ${call.method}');
          return null;
      }
    } catch (e) {
      debugPrint('Voice Method Channel: Error handling method call - $e');
      return null;
    }
  }

  /// Handle Google Assistant intent
  Future<bool> _handleAssistantIntent(dynamic arguments) async {
    try {
      final Map<String, dynamic> data = Map<String, dynamic>.from(arguments);
      debugPrint('Voice Method Channel: Handling Assistant intent - $data');
      
      await _voiceAssistant.handleAssistantIntent(data);
      return true;
    } catch (e) {
      debugPrint('Voice Method Channel: Error handling Assistant intent - $e');
      return false;
    }
  }

  /// Handle deep link
  Future<bool> _handleDeepLink(dynamic arguments) async {
    try {
      final Map<String, dynamic> data = Map<String, dynamic>.from(arguments);
      final String uriString = data['uri'] as String;
      final Uri uri = Uri.parse(uriString);
      
      debugPrint('Voice Method Channel: Handling deep link - $uri');
      
      await _voiceAssistant.handleDeepLink(uri);
      return true;
    } catch (e) {
      debugPrint('Voice Method Channel: Error handling deep link - $e');
      return false;
    }
  }

  /// Send voice command to native side (if needed)
  Future<bool> sendVoiceCommand(String command, Map<String, dynamic> parameters) async {
    try {
      final result = await _channel.invokeMethod('handleVoiceCommand', {
        'command': command,
        'parameters': parameters,
      });
      
      return result == true;
    } catch (e) {
      debugPrint('Voice Method Channel: Error sending voice command - $e');
      return false;
    }
  }

  /// Get the voice assistant service instance
  VoiceAssistantService get voiceAssistant => _voiceAssistant;

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;
}