import 'package:flutter_test/flutter_test.dart';
import 'package:phoenix/models/voice_command_model.dart';
import 'package:phoenix/features/conversation/bloc/conversation_bloc.dart';

/// Test to verify the voice command flow and response generation
void main() {
  group('Voice Command Flow Tests', () {
    test('Buy command should not generate premature confirmation', () {
      // Create a buy command
      final buyCommand = VoiceCommand(
        action: VoiceCommandAction.buy,
        symbol: 'DHANUSH', // Invalid symbol
        quantity: 10,
      );

      // Verify the command is complete
      expect(buyCommand.isComplete, true);
      expect(buyCommand.symbol, 'DHANUSH');
      expect(buyCommand.quantity, 10);
      expect(buyCommand.action, VoiceCommandAction.buy);
    });

    test('Sell command should not generate premature confirmation', () {
      // Create a sell command
      final sellCommand = VoiceCommand(
        action: VoiceCommandAction.sell,
        symbol: 'INVALID', // Invalid symbol
        quantity: 5,
      );

      // Verify the command is complete
      expect(sellCommand.isComplete, true);
      expect(sellCommand.symbol, 'INVALID');
      expect(sellCommand.quantity, 5);
      expect(sellCommand.action, VoiceCommandAction.sell);
    });

    test('Quote command should not generate premature confirmation', () {
      // Create a quote command
      final quoteCommand = VoiceCommand(
        action: VoiceCommandAction.getQuote,
        symbol: 'NONEXISTENT', // Invalid symbol
      );

      // Verify the command is complete
      expect(quoteCommand.isComplete, true);
      expect(quoteCommand.symbol, 'NONEXISTENT');
      expect(quoteCommand.action, VoiceCommandAction.getQuote);
    });

    test('Voice command validation should catch invalid symbols', () {
      // Test various invalid symbols that should be caught by validation
      final invalidSymbols = [
        'DHANUSH',
        'INVALID',
        'NONEXISTENT',
        'FAKE',
        'NOTREAL',
      ];

      for (final symbol in invalidSymbols) {
        final command = VoiceCommand(
          action: VoiceCommandAction.buy,
          symbol: symbol,
          quantity: 1,
        );

        // Command should be syntactically complete
        expect(command.isComplete, true);
        
        // But security validation should catch these as invalid
        // (This would be tested in the actual VoiceCommandHandler)
        expect(command.symbol, symbol);
      }
    });

    test('Valid symbols should be recognizable', () {
      // Test some common valid symbols that should exist
      final potentiallyValidSymbols = [
        'TCS',
        'RELIANCE',
        'INFY',
        'HDFCBANK',
        'ICICIBANK',
      ];

      for (final symbol in potentiallyValidSymbols) {
        final command = VoiceCommand(
          action: VoiceCommandAction.buy,
          symbol: symbol,
          quantity: 1,
        );

        // Command should be syntactically complete
        expect(command.isComplete, true);
        expect(command.symbol, symbol);
      }
    });
  });
}
