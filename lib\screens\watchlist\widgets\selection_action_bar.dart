import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import '../../../features/theme/bloc/theme_bloc.dart';
import '../../../features/theme/bloc/theme_state.dart';

class SelectionActionBar extends StatelessWidget {
  final int selectedCount;
  final VoidCallback onDelete;
  final VoidCallback onClear;

  const SelectionActionBar({
    super.key,
    required this.selectedCount,
    required this.onDelete,
    required this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.fastOutSlowIn,
          height: selectedCount > 0 ? 70 : 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: selectedCount > 0 ? 1.0 : 0.0,
            curve: Curves.easeOut,
            child: AnimatedScale(
              duration: const Duration(milliseconds: 250),
              scale: selectedCount > 0 ? 1.0 : 0.95,
              curve: Curves.fastOutSlowIn,
              child: selectedCount > 0
                  ? Container(
                      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor(themeState.isDarkMode),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: themeState.isDarkMode 
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.grey.withValues(alpha: 0.2),
                            offset: const Offset(0, -2),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                          BoxShadow(
                            color: themeState.isDarkMode 
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.white.withValues(alpha: 0.8),
                            offset: const Offset(0, 2),
                            blurRadius: 5,
                            inset: true,
                          ),
                        ],
                      ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Selection count
                  Container(
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.5),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '$selectedCount selected',
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          height: 1.0,
                        ),
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Clear selection button
                  InkWell(
                    onTap: onClear,
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(
                        color: AppTheme.textSecondary(themeState.isDarkMode).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.clear,
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          size: 18,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Delete button
                  InkWell(
                    onTap: onDelete,
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      height: 40,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: ThemeConstants.titleRedColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: ThemeConstants.titleRedColor.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.delete_outline,
                              color: ThemeConstants.titleRedColor,
                              size: 18,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'Delete',
                              style: TextStyle(
                                color: ThemeConstants.titleRedColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                height: 1.0,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),
        );
      },
    );
  }
}