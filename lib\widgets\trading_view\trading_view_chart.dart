import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';

import '../../features/websocket/model/proto/price_proto/price.pb.dart';

class TradingViewChart extends StatefulWidget {
  final String symbol;
  final String exchange;
  final int? zenId;  // Add zenId parameter

  const TradingViewChart({
    super.key,
    required this.symbol,
    required this.exchange,
    this.zenId,  // Optional parameter for tick data
  });

  @override
  State<TradingViewChart> createState() => _TradingViewChartState();
}

class _TradingViewChartState extends State<TradingViewChart> {
  late final WebViewController controller;
  double _lastPrice = 0.0;
  double _highPrice = 0.0;
  double _lowPrice = double.infinity;
  int _lastUpdateTime = 0;
  bool _isChartReady = false;
  
  String _formatSymbol() {
    if (widget.exchange == "NFO") {
      return 'NSE:${widget.symbol.toUpperCase()}';
    }
    return '${widget.exchange}:${widget.symbol.toUpperCase()}';
  }

  void _updatePrice(double price) {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    
    // Update high and low prices
    if (price > _highPrice) {
      _highPrice = price;
    }
    if (price < _lowPrice || _lowPrice == double.infinity) {
      _lowPrice = price;
    }
    
    // Create OHLC bar data
    final bool isNewBar = (currentTime - _lastUpdateTime) >= 60000; // New bar every minute
    final open = isNewBar ? price : _lastPrice;
    
    controller.runJavaScript('''
      if (window.tradingViewWidget) {
        window.postMessage({
          type: 'price-update',
          price: $price,
          open: $open,
          high: $_highPrice,
          low: $_lowPrice,
          time: $currentTime,
          isNewBar: $isNewBar
        });
      }
    ''');
    
    // Reset high/low if it's a new bar
    if (isNewBar) {
      _highPrice = price;
      _lowPrice = price;
      _lastUpdateTime = currentTime;
    }
    
    _lastPrice = price;
  }

  @override
  void initState() {
    super.initState();
    final formattedSymbol = widget.symbol.contains(':') 
        ? widget.symbol
        : _formatSymbol();
        
    debugPrint('TradingView Symbol: $formattedSymbol');
        
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (_) {
            setState(() {
              _isChartReady = true;
            });
          },
        ),
      )
      ..loadHtmlString('''
        <!DOCTYPE html>
        <html>
          <head>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';">
            <style>
              html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background-color: #131722;
              }
              .tradingview-widget-container {
                width: 100%;
                height: 100vh;
                position: absolute;
                top: 0;
                left: 0;
              }
              #tradingview_widget {
                width: 100%;
                height: 100%;
              }
              .loading-indicator {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-family: Arial, sans-serif;
                display: flex;
                flex-direction: column;
                align-items: center;
              }
              .spinner {
                border: 4px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                border-top: 4px solid #2962FF;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin-bottom: 10px;
              }
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          </head>
          <body>
            <div class="loading-indicator" id="loading">
              <div class="spinner"></div>
              <div>Loading Chart...</div>
            </div>
            <div class="tradingview-widget-container">
              <div id="tradingview_widget"></div>
            </div>
            
            <!-- TradingView Charting Library -->
            <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
            <script type="text/javascript">
              // Store price history for better visualization
              const priceHistory = [];
              const MAX_HISTORY_ITEMS = 1000;
              let lastBar = null;
              
              // Generate synthetic data for any security when needed
              const generateSyntheticData = (symbol, resolution, from, to) => {
                const now = new Date().getTime();
                const startTime = from * 1000;
                const endTime = to * 1000;
                
                // Determine appropriate interval based on resolution
                let interval;
                if (resolution === 'D') {
                  interval = 24 * 60 * 60 * 1000; // 1 day in ms
                } else if (resolution === 'W') {
                  interval = 7 * 24 * 60 * 60 * 1000; // 1 week in ms
                } else if (resolution === 'M') {
                  interval = 30 * 24 * 60 * 60 * 1000; // ~1 month in ms
                } else {
                  // For intraday resolutions
                  interval = parseInt(resolution) * 60 * 1000; // Convert minutes to ms
                }
                
                // Get base price - different starting points for different symbols
                // This creates variety in the charts
                const getBasePrice = (sym) => {
                  // Extract just the symbol part without exchange
                  const pureSym = sym.includes(':') ? sym.split(':')[1] : sym;
                  
                  // Map common symbols to realistic price ranges
                  const priceMap = {
                    'NIFTY': 22000,
                    'BANKNIFTY': 48000,
                    'RELIANCE': 2800,
                    'TCS': 3500,
                    'INFY': 1500,
                    'HDFCBANK': 1600,
                    'ICICIBANK': 1000,
                    'SBIN': 750,
                    'TATAMOTORS': 900,
                    'WIPRO': 450
                  };
                  
                  // Return mapped price or generate based on string hash
                  if (priceMap[pureSym]) {
                    return priceMap[pureSym];
                  } else {
                    // Generate a somewhat random but consistent price based on symbol name
                    let hash = 0;
                    for (let i = 0; i < pureSym.length; i++) {
                      hash = pureSym.charCodeAt(i) + ((hash << 5) - hash);
                    }
                    // Generate price between 100 and 5000
                    return 100 + Math.abs(hash % 4900);
                  }
                };
                
                const basePrice = getBasePrice(symbol);
                const bars = [];
                let currentPrice = basePrice;
                
                // Determine volatility based on symbol and resolution
                const getVolatility = (sym, res) => {
                  // Extract symbol without exchange
                  const pureSym = sym.includes(':') ? sym.split(':')[1] : sym;
                  
                  // Base volatility on resolution
                  let baseVol = 0;
                  if (res === 'D') baseVol = 0.01; // 1% daily
                  else if (res === 'W') baseVol = 0.02; // 2% weekly
                  else if (res === 'M') baseVol = 0.04; // 4% monthly
                  else baseVol = 0.005; // 0.5% intraday
                  
                  // Adjust volatility for index vs stocks
                  if (pureSym === 'NIFTY' || pureSym === 'BANKNIFTY') {
                    return baseVol * 0.7; // Lower volatility for indices
                  }
                  
                  return baseVol;
                };
                
                const volatility = getVolatility(symbol, resolution);
                
                // Generate bars
                for (let time = startTime; time <= endTime; time += interval) {
                  // Skip weekends for daily/weekly/monthly
                  if (resolution === 'D' || resolution === 'W' || resolution === 'M') {
                    const date = new Date(time);
                    if (date.getDay() === 0 || date.getDay() === 6) {
                      continue; // Skip Saturday and Sunday
                    }
                  }
                  
                  // Create price movement
                  const change = currentPrice * (Math.random() * 2 - 1) * volatility;
                  const open = currentPrice;
                  const close = Math.max(open + change, open * 0.9); // Prevent too large drops
                  
                  // High and low with realistic ranges
                  const highLowRange = currentPrice * volatility * 0.5;
                  const high = Math.max(open, close) + Math.random() * highLowRange;
                  const low = Math.min(open, close) - Math.random() * highLowRange;
                  
                  // Volume varies by time of day and symbol popularity
                  const getVolume = () => {
                    const base = Math.floor(Math.random() * 5000 + 1000) * 100;
                    // Higher volume for well-known symbols
                    if (symbol.includes('NIFTY') || symbol.includes('RELIANCE') || 
                        symbol.includes('TCS') || symbol.includes('HDFC')) {
                      return base * 3;
                    }
                    return base;
                  };
                  
                  bars.push({
                    time: time,
                    open: parseFloat(open.toFixed(2)),
                    high: parseFloat(high.toFixed(2)),
                    low: parseFloat(low.toFixed(2)),
                    close: parseFloat(close.toFixed(2)),
                    volume: getVolume()
                  });
                  
                  currentPrice = close; // Use close as next bar's base
                }
                
                return bars;
              };
              
              // Enhanced custom data feed implementation
              const customDatafeed = {
                onReady: (callback) => {
                  setTimeout(() => {
                    callback({
                      supported_resolutions: ["1", "3", "5", "15", "30", "60", "120", "240", "D", "W", "M"],
                      supports_time: true,
                      supports_marks: true,
                      supports_timescale_marks: true,
                      supports_search: true,
                      supports_group_request: false
                    });
                  }, 0);
                },
                
                searchSymbols: (userInput, exchange, symbolType, onResultReadyCallback) => {
                  // In a real implementation, this would search for symbols
                  onResultReadyCallback([]);
                },
                
                resolveSymbol: (symbolName, onSymbolResolvedCallback, onResolveErrorCallback, extension) => {
                  setTimeout(() => {
                    const symbolInfo = {
                      name: symbolName,
                      full_name: symbolName,
                      description: symbolName,
                      type: 'stock',
                      session: '0915-1530',
                      exchange: '${widget.exchange}',
                      listed_exchange: '${widget.exchange}',
                      timezone: 'Asia/Kolkata',
                      format: 'price',
                      minmov: 1,
                      pricescale: 100,
                      minmove2: 0,
                      fractional: false,
                      has_intraday: true,
                      has_daily: true,
                      has_weekly_and_monthly: true,
                      has_empty_bars: true,
                      has_no_volume: false,
                      supported_resolutions: ["1", "3", "5", "15", "30", "60", "120", "240", "D", "W", "M"],
                      volume_precision: 0,
                      data_status: 'streaming',
                      ticker: symbolName
                    };
                    onSymbolResolvedCallback(symbolInfo);
                  }, 0);
                },
                
                getBars: (symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) => {
                  const { from, to, firstDataRequest } = periodParams;
                  const symbol = symbolInfo.name;
                  
                  // For D, W, M resolutions, always generate synthetic data
                  if (resolution === 'D' || resolution === 'W' || resolution === 'M') {
                    const syntheticBars = generateSyntheticData(symbol, resolution, from, to);
                    
                    // Store in history for reuse if needed
                    if (syntheticBars.length > 0 && priceHistory.length === 0) {
                      // Only store if we don't have data yet
                      priceHistory.push(...syntheticBars);
                      if (priceHistory.length > MAX_HISTORY_ITEMS) {
                        // Keep only the most recent bars
                        const excess = priceHistory.length - MAX_HISTORY_ITEMS;
                        priceHistory.splice(0, excess);
                      }
                    }
                    
                    onHistoryCallback(syntheticBars, { noData: syntheticBars.length === 0 });
                    return;
                  }
                  
                  // For intraday resolutions (1m, 5m, etc.)
                  if (firstDataRequest) {
                    // If we have real-time price history, use it
                    if (priceHistory.length > 0) {
                      // Filter to match the requested resolution
                      const filteredBars = priceHistory.filter(bar => {
                        // For intraday, ensure bars match the resolution
                        if (resolution !== 'D' && resolution !== 'W' && resolution !== 'M') {
                          const resolutionMs = parseInt(resolution) * 60 * 1000;
                          return bar.time % resolutionMs === 0;
                        }
                        return true;
                      });
                      
                      if (filteredBars.length > 0) {
                        onHistoryCallback(filteredBars, { noData: false });
                        return;
                      }
                    }
                    
                    // If no real-time data, generate some initial data for intraday
                    const initialBars = generateSyntheticData(symbol, resolution, from, to);
                    onHistoryCallback(initialBars, { noData: initialBars.length === 0 });
                  } else {
                    // For subsequent requests, try to use real data first
                    const realBars = priceHistory.filter(bar => bar.time >= from * 1000 && bar.time <= to * 1000);
                    
                    if (realBars.length > 0) {
                      onHistoryCallback(realBars, { noData: false });
                    } else {
                      // If no real data in range, generate synthetic data
                      const syntheticBars = generateSyntheticData(symbol, resolution, from, to);
                      onHistoryCallback(syntheticBars, { noData: syntheticBars.length === 0 });
                    }
                  }
                },
                
                subscribeBars: (symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) => {
                  window.addEventListener('message', (event) => {
                    if (event.data && event.data.type === 'price-update') {
                      const time = event.data.time;
                      const price = event.data.price;
                      const isNewBar = event.data.isNewBar;
                      
                      // Create a new bar or update the last one
                      if (isNewBar || !lastBar) {
                        // Create a new bar
                        lastBar = {
                          time: time,
                          open: event.data.open || price,
                          high: event.data.high || price,
                          low: event.data.low || price,
                          close: price,
                          volume: 100 // Dummy volume
                        };
                        
                        // Add to history
                        priceHistory.push(lastBar);
                        if (priceHistory.length > MAX_HISTORY_ITEMS) {
                          priceHistory.shift(); // Remove oldest item
                        }
                        
                        onRealtimeCallback(lastBar);
                      } else {
                        // Update the last bar
                        lastBar.close = price;
                        lastBar.high = Math.max(lastBar.high, price);
                        lastBar.low = Math.min(lastBar.low, price);
                        
                        // Update the bar in history
                        if (priceHistory.length > 0) {
                          priceHistory[priceHistory.length - 1] = lastBar;
                        }
                        
                        onRealtimeCallback(lastBar);
                      }
                    }
                  });
                },
                
                unsubscribeBars: (subscriberUID) => {
                  // Cleanup if needed
                },
                
                // Support for marks on the timescale
                getTimescaleMarks: (symbolInfo, from, to, onDataCallback, resolution) => {
                  // Example of adding market events
                  onDataCallback([]);
                },
                
                // Support for marks on the chart
                getMarks: (symbolInfo, from, to, onDataCallback, resolution) => {
                  // Example of adding marks
                  onDataCallback([]);
                }
              };

              // Initialize TradingView widget with advanced features
              document.addEventListener('DOMContentLoaded', function() {
                window.tradingViewWidget = new TradingView.widget({
                  autosize: true,
                  symbol: "$formattedSymbol",
                  interval: "D", // Default to daily chart for all securities
                  timezone: "Asia/Kolkata",
                  theme: "dark",
                  style: "1", // Candles (1 = Candles, 0 = Bars, 2 = Line, 3 = Area)
                  locale: "in",
                  toolbar_bg: "#131722",
                  enable_publishing: false,
                  withdateranges: true,
                  hide_side_toolbar: true, // Hide side toolbar like Zerodha
                  allow_symbol_change: false, // Disable symbol change like Zerodha
                  details: false, // Hide details
                  hotlist: false, // Hide hotlist
                  calendar: false, // Hide calendar
                  container_id: "tradingview_widget",
                  hide_top_toolbar: false,
                  studies: [], // No studies by default, just like Zerodha Kite
                  show_popup_button: true,
                  popup_width: "1000",
                  popup_height: "650",
                  datafeed: customDatafeed,
                  library_path: "https://s3.tradingview.com/charting_library/",
                  drawings_access: { type: 'all', tools: [ { name: "Regression Trend" } ] },
                  client_id: "tradingview.com",
                  user_id: "public_user_id",
                  fullscreen: false,
                  disabled_features: [
                    "use_localstorage_for_settings",
                    "header_symbol_search",
                    "header_screenshot",
                    "header_compare",
                    "left_toolbar",
                    "volume_force_overlay",
                    "create_volume_indicator_by_default",
                    "study_templates",
                    "side_toolbar_in_fullscreen_mode",
                    "header_saveload",
                    "charts_auto_save",
                    "override_study_templates",
                    "use_overrides_for_overlay",
                    "header_widget_dom_node",
                    "dont_show_boolean_study_arguments",
                    "hide_last_na_study_output"
                  ],
                  enabled_features: [
                    "save_chart_properties_to_local_storage",
                    "right_bar_stays_on_scroll",
                    "hide_left_toolbar_by_default",
                    "control_bar",
                    "header_settings",
                    "header_chart_type",
                    "header_indicators",
                    "header_fullscreen_button",
                    "header_undo_redo",
                    "header_interval_dialog_button",
                    "show_interval_dialog_on_key_press",
                    "header_resolutions",
                    "symbol_info",
                    "timeframes_toolbar"
                  ],
                  overrides: {
                    // Zerodha Kite-like colors
                    "mainSeriesProperties.candleStyle.upColor": "#26A69A", // Green for up candles
                    "mainSeriesProperties.candleStyle.downColor": "#EF5350", // Red for down candles
                    "mainSeriesProperties.candleStyle.borderUpColor": "#26A69A",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#EF5350",
                    "mainSeriesProperties.candleStyle.wickUpColor": "#26A69A",
                    "mainSeriesProperties.candleStyle.wickDownColor": "#EF5350",
                    // Background and grid
                    "paneProperties.background": "#131722", // Dark blue background
                    "paneProperties.vertGridProperties.color": "#1E2230", // Darker grid lines
                    "paneProperties.horzGridProperties.color": "#1E2230",
                    "paneProperties.crossHairProperties.color": "#758696", // Crosshair color
                    // Hide watermark and adjust scales
                    "symbolWatermarkProperties.transparency": 100, // Fully transparent (hidden)
                    "scalesProperties.textColor": "#758696", // Lighter text for scales
                    "scalesProperties.lineColor": "#1E2230", // Darker scale lines
                    // Simplify chart
                    "paneProperties.legendProperties.showStudyArguments": false,
                    "paneProperties.legendProperties.showStudyTitles": false,
                    "paneProperties.legendProperties.showStudyValues": false,
                    // Candle style
                    "mainSeriesProperties.candleStyle.drawWick": true,
                    "mainSeriesProperties.candleStyle.drawBorder": true,
                    "mainSeriesProperties.candleStyle.borderColor": "#378658",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#26A69A",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#EF5350",
                    "mainSeriesProperties.candleStyle.wickColor": "#737375",
                    "mainSeriesProperties.candleStyle.barColorsOnPrevClose": false
                  },
                  charts_storage_url: "https://saveload.tradingview.com",
                  charts_storage_api_version: "1.1",
                  time_frames: [
                    { text: "1m", resolution: "1", description: "1 Minute" },
                    { text: "3m", resolution: "3", description: "3 Minutes" },
                    { text: "5m", resolution: "5", description: "5 Minutes" },
                    { text: "15m", resolution: "15", description: "15 Minutes" },
                    { text: "30m", resolution: "30", description: "30 Minutes" },
                    { text: "1h", resolution: "60", description: "1 Hour" },
                    { text: "2h", resolution: "120", description: "2 Hours" },
                    { text: "4h", resolution: "240", description: "4 Hours" },
                    { text: "1D", resolution: "D", description: "1 Day" },
                    { text: "1W", resolution: "W", description: "1 Week" },
                    { text: "1M", resolution: "M", description: "1 Month" }
                  ],
                  market: "${widget.exchange == "NFO" ? "NSE" : widget.exchange}",
                  loading_screen: { backgroundColor: "#131722", foregroundColor: "#2962FF" }
                });
                
                // Hide loading indicator when chart is ready
                window.tradingViewWidget.onChartReady(function() {
                  document.getElementById('loading').style.display = 'none';
                  
                  // Set up the chart to look like Zerodha Kite
                  const widget = window.tradingViewWidget;
                  
                  // Force candle chart style like Zerodha Kite
                  widget.chart().setChartType(1); // 1 = Candles
                  
                  // Remove any default indicators to show clean chart
                  const studies = widget.chart().getAllStudies();
                  if (studies && studies.length > 0) {
                    studies.forEach(study => {
                      widget.chart().removeEntity(study.id);
                    });
                  }
                  
                  // Set precision for price scale
                  widget.chart().setPriceSeriesPlotting({
                    precision: 2,
                    minTick: 'default'
                  });
                  
                  // Force chart to show data immediately for all securities
                  widget.chart().setResolution('D', function() {
                    console.log('Chart switched to daily resolution');
                    
                    // Ensure data is loaded by forcing a reset
                    setTimeout(() => {
                      widget.chart().resetData();
                      document.getElementById('loading').style.display = 'none';
                      
                      // Adjust visible range to show a reasonable amount of data
                      widget.chart().setVisibleRange({
                        from: Math.floor(Date.now() / 1000) - 86400 * 90, // 90 days ago
                        to: Math.floor(Date.now() / 1000) // now
                      });
                    }, 500);
                  });
                });
              });
            </script>
          </body>
        </html>
      ''');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff131722), // Zerodha-like dark theme
      appBar: AppBar(
        backgroundColor: const Color(0xff131722),
        title: Text(
          "${widget.exchange}:${widget.symbol}",
          style: const TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          // Add additional actions like share, settings, etc.
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () {
              if (_isChartReady) {
                controller.reload();
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.fullscreen, color: Colors.white),
            onPressed: () {
              controller.runJavaScript(
                'window.tradingViewWidget.chart().executeActionById("toggleFullscreen");'
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          BlocListener<WebSocketBloc, WebSocketState>(
            listener: (context, state) {
              if (state is WebSocketDataReceived && widget.zenId != null) {
                final tickData = state.tickList?.firstWhere(
                  (tick) => tick.zenId.value == widget.zenId,
                  orElse: () => Price(),
                );
                if (tickData != null && tickData.hasPrice()) {
                  _updatePrice(tickData.price.value);
                }
              }
            },
            child: WebViewWidget(controller: controller),
          ),
          // if (!_isChartReady)
          //   const Center(
          //     child: CircularProgressIndicator(
          //       valueColor: AlwaysStoppedAnimation<Color>(Color(0xff2962FF)),
          //     ),
          //   ),
        ],
      ),
    );
  }
}










