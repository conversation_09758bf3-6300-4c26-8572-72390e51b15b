import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Enum for message types in the conversation
enum MessageType {
  user,
  assistant,
  system,
  error,
}

/// Enum for message status
enum MessageStatus {
  sending,
  sent,
  processing,
  completed,
  failed,
}

/// Model representing a single message in the conversation
class ConversationMessage extends Equatable {
  final String id;
  final MessageType type;
  final String content;
  final MessageStatus status;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  final String? voiceCommandId;
  final String? originalText;
  final bool isSpoken;

  ConversationMessage({
    String? id,
    required this.type,
    required this.content,
    this.status = MessageStatus.sent,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
    this.voiceCommandId,
    this.originalText,
    this.isSpoken = false,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now(),
        metadata = metadata ?? {};

  /// Create user message from voice input
  factory ConversationMessage.userVoice({
    required String originalText,
    required String processedText,
    String? voiceCommandId,
  }) {
    return ConversationMessage(
      type: MessageType.user,
      content: processedText.isNotEmpty ? processedText : originalText,
      originalText: originalText,
      voiceCommandId: voiceCommandId,
      metadata: {
        'inputMethod': 'voice',
        'processedText': processedText,
      },
    );
  }

  /// Create assistant response message
  factory ConversationMessage.assistantResponse({
    required String content,
    bool isSpoken = true,
    String? voiceCommandId,
    Map<String, dynamic>? additionalMetadata,
  }) {
    return ConversationMessage(
      type: MessageType.assistant,
      content: content,
      isSpoken: isSpoken,
      voiceCommandId: voiceCommandId,
      metadata: {
        'isSpoken': isSpoken,
        ...?additionalMetadata,
      },
    );
  }

  /// Create system message (for status updates, etc.)
  factory ConversationMessage.system({
    required String content,
    Map<String, dynamic>? metadata,
  }) {
    return ConversationMessage(
      type: MessageType.system,
      content: content,
      status: MessageStatus.completed,
      metadata: metadata ?? {},
    );
  }

  /// Create error message
  factory ConversationMessage.error({
    required String content,
    String? voiceCommandId,
    Map<String, dynamic>? metadata,
  }) {
    return ConversationMessage(
      type: MessageType.error,
      content: content,
      status: MessageStatus.failed,
      voiceCommandId: voiceCommandId,
      metadata: metadata ?? {},
    );
  }

  /// Copy with updated status
  ConversationMessage copyWithStatus(MessageStatus newStatus) {
    return ConversationMessage(
      id: id,
      type: type,
      content: content,
      status: newStatus,
      timestamp: timestamp,
      metadata: metadata,
      voiceCommandId: voiceCommandId,
      originalText: originalText,
      isSpoken: isSpoken,
    );
  }

  /// Copy with updated content
  ConversationMessage copyWithContent(String newContent) {
    return ConversationMessage(
      id: id,
      type: type,
      content: newContent,
      status: status,
      timestamp: timestamp,
      metadata: metadata,
      voiceCommandId: voiceCommandId,
      originalText: originalText,
      isSpoken: isSpoken,
    );
  }

  /// Get display text for the message
  String get displayText {
    switch (type) {
      case MessageType.user:
        return originalText ?? content;
      case MessageType.assistant:
      case MessageType.system:
      case MessageType.error:
        return content;
    }
  }

  /// Check if message is from user
  bool get isUser => type == MessageType.user;

  /// Check if message is from assistant
  bool get isAssistant => type == MessageType.assistant;

  /// Check if message is system/status message
  bool get isSystem => type == MessageType.system;

  /// Check if message represents an error
  bool get isError => type == MessageType.error;

  /// Check if message is still being processed
  bool get isProcessing => status == MessageStatus.processing || status == MessageStatus.sending;

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'content': content,
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
      'voiceCommandId': voiceCommandId,
      'originalText': originalText,
      'isSpoken': isSpoken,
    };
  }

  /// Create from JSON
  factory ConversationMessage.fromJson(Map<String, dynamic> json) {
    return ConversationMessage(
      id: json['id'] as String,
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.system,
      ),
      content: json['content'] as String,
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      voiceCommandId: json['voiceCommandId'] as String?,
      originalText: json['originalText'] as String?,
      isSpoken: json['isSpoken'] as bool? ?? false,
    );
  }

  @override
  String toString() {
    return 'ConversationMessage(id: $id, type: $type, content: $content, status: $status)';
  }

  @override
  List<Object?> get props => [
        id,
        type,
        content,
        status,
        timestamp,
        metadata,
        voiceCommandId,
        originalText,
        isSpoken,
      ];
}