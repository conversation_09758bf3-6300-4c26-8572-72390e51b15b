import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';

class InnerShadowButton extends StatelessWidget {
  final Future<void> Function() action;

  const InnerShadowButton({super.key, required this.action});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: InkWell(
        onTap: () async {
          await action();
        },
        child: Container(
          width: 350,
          height: 50,
          decoration: BoxDecoration(
            color: const Color(0xFF338AFF),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.9),
                offset: Offset(-10, -10),
                blurRadius: 20,
                inset: true,
              ),
              BoxShadow(
                color: const Color.fromARGB(255, 10, 113, 248),
                // color:  Color(0xFF2A2D33) ,
                offset: Offset(10, 10),
                blurRadius: 20,
                inset: true,
              ),
            ],
          ),
          child: const Center(
            child: Text(
              'Get Started',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
