import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fuzzy/fuzzy.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
/*
This service is used to search the security list in the order form.
Enhanced to support searching by name field:
- For equities: name contains company full names (e.g., "TCS" -> "Tata Consultancy Service")
  Uses contains matching so "steel" finds both "Tata Steel" and "JSW Steel"
- For F&O: name contains underlying names (e.g., "Nifty 50", "Bank Nifty", "Nifty Midcap 50")
  Uses exact word matching so "nifty" finds only "Nifty 50", not "Nifty Midcap 50"
*/

class SecurityListSearchService {
  // Set for quick lookup of known option types
  static final Set<String> _optionTypes = {'ce', 'pe', 'fut'};

  /// Extracts the symbol group from the trading symbol (e.g. "nifty25julfut" → "nifty")
  /// We prioritize longer matches first (e.g. "midcpnifty" over "nifty")
  static String? _extractSymbolGroup(String tradingSymbol) {
    final knownGroups = ['midcpnifty', 'banknifty', 'finnifty', 'nifty'];
    final lowerSymbol = tradingSymbol.toLowerCase();

    for (final group in knownGroups
      ..sort((a, b) => b.length.compareTo(a.length))) {
      if (lowerSymbol.startsWith(group)) return group;
    }
    return null;
  }

  /// Cleans up and splits user query into lowercase tokens.
  /// Example: "Nifty 25th July CE 25000" → ["nifty", "25", "july", "ce", "25000"]
  static List<String> _normalizeTokens(String query) {
    return query
        .toLowerCase()
        .replaceAll(RegExp(r'(st|nd|rd|th)'), '') // remove ordinal suffixes
        .replaceAll(RegExp(r'[^a-z0-9 ]'), ' ') // remove special characters
        .split(RegExp(r'\s+')) // split on whitespace
        .where((t) => t.isNotEmpty)
        .toList();
  }

  /// Parses the tokens from the F&O query into structured fields.
  /// Returns: group, month, day, optionType, strike.
  static Map<String, dynamic> _parseFNOQuery(List<String> tokens) {
    final monthMap = {
      'jan': 1,
      'feb': 2,
      'mar': 3,
      'apr': 4,
      'may': 5,
      'jun': 6,
      'jul': 7,
      'aug': 8,
      'sep': 9,
      'oct': 10,
      'nov': 11,
      'dec': 12,
      'january': 1,
      'february': 2,
      'march': 3,
      'april': 4,
      'june': 6,
      'july': 7,
      'august': 8,
      'september': 9,
      'october': 10,
      'november': 11,
      'december': 12,
    };

    String? group;
    int? month;
    int? day;
    String? optionType;
    String? strike;
    String? expiryType;

    for (final raw in tokens) {
      final t = raw.toLowerCase();
      if (group == null) {
        final g = _extractSymbolGroup(t);
        if (g != null) group = g;
      }
      if (monthMap.containsKey(t)) month = monthMap[t];
      if (RegExp(r'^\d{1,2}$').hasMatch(t)) day = int.parse(t);
      if (_optionTypes.contains(t)) optionType = t.toUpperCase();
      if (RegExp(r'^\d{4,6}$').hasMatch(t)) strike = t;
      if (t == 'm' || t == 'monthly') expiryType = 'MONTHLY';
      if (t == 'w' || t == 'weekly') expiryType = 'WEEKLY';
    }

    debugPrint(
        "🔍group :$group month: $month day: $day optionType: $optionType strike: $strike expiryType: $expiryType");

    return {
      'group': group,
      'month': month,
      'day': day,
      'optionType': optionType,
      'strike': strike,
      'expiryType': expiryType,
    };
  }

  /// Filters the F&O list based on parsed query
  static List<SecurityModel> _filterFNO(
    List<SecurityModel> list,
    Map<String, dynamic> q,
  ) {
    return list.where((sec) {
      final symbol = sec.tradingSymbol.toLowerCase();
      final expiry = _parseExpiry(sec.expiry);
      final secGroup = _extractSymbolGroup(symbol);

      // Match symbol group if present i.e nifty banknifty
      // Also check in name field for F&O underlying names with exact word matching
      if (q['group'] != null) {
        final groupMatches = secGroup == q['group'] ||
            _matchesNameField(sec.name, q['group']!, isEquity: false);
        if (!groupMatches) return false;
      }

      // Match expiry month
      if (q['month'] != null &&
          (expiry == null || expiry.month != q['month'])) {
        return false;
      }

      // Match expiry day
      if (q['day'] != null && (expiry == null || expiry.day != q['day'])) {
        return false;
      }

      // Match option type (CE/PE/FUT)
      if (q['optionType'] != null &&
          !symbol.endsWith(q['optionType']!.toLowerCase())) {
        return false;
      }

      // Match strike price
      if (q['strike'] != null && !symbol.contains(q['strike']!)) return false;

      // Match expiry type (MONTHLY/WEEKLY)
      if (q['expiryType'] != null) {
        if (sec.expiryType == null) return false;

        final queryExpiryType = q['expiryType'] as String;
        final securityExpiryType = sec.expiryType!.toUpperCase();

        if (queryExpiryType == 'MONTHLY' && securityExpiryType != 'MONTHLY') {
          return false;
        }
        if (queryExpiryType == 'WEEKLY' && securityExpiryType != 'WEEKLY') {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// Optimized universal search for both equity and F&O
  static void optimizedSearch(
    String query,
    BuildContext context,
    String type,
    Function clear,
    Function(List<SecurityModel>) setResults,
  ) {
    debugPrint("Search Query: $query");
    final state = context.read<SecurityListBloc>().state;
    if (state is! SecurityListLoaded) {
      clear();
      return;
    }
    final List<SecurityModel> sourceList =
        type == 'equity' ? state.equityList : state.featuresList;
    if (query.trim().isEmpty) {
      clear();
      return;
    }
    final tokens = _normalizeTokens(query);
    final loweredQuery = query.toLowerCase();

    // Match buckets
    final exactMatches = <SecurityModel>[];
    final prefixMatches = <SecurityModel>[];
    final substringMatches = <SecurityModel>[];
    final nameMatches = <SecurityModel>[];
    final seen = <String>{};

    for (final s in sourceList) {
      final symbolLower = s.tradingSymbol.toLowerCase();

      if (symbolLower == loweredQuery) {
        exactMatches.add(s);
        seen.add(symbolLower);
      } else if (symbolLower.startsWith(loweredQuery)) {
        prefixMatches.add(s);
        seen.add(symbolLower);
      } else if (symbolLower.contains(loweredQuery)) {
        substringMatches.add(s);
        seen.add(symbolLower);
      } else if (_matchesNameField(s.name, query, isEquity: type == 'equity')) {
        nameMatches.add(s);
        seen.add(symbolLower);
      }
    }

    // ❌ Remove fuzzy for equity (unrelated results)
    List<SecurityModel> fuzzyMatches = [];
    if (type != 'equity') {
      // Keep F&O structured filtering
      fuzzyMatches = [];
    }

    // 3. F&O structured filter (for F&O only)
    List<SecurityModel> fnoMatches = [];
    if (type != 'equity' && tokens.isNotEmpty) {
      fnoMatches =
          sortByFNOFields(_filterFNO(sourceList, _parseFNOQuery(tokens)))
              .where((s) => !seen.contains(s.tradingSymbol.toLowerCase()))
              .toList();
    }

    // Combine and limit
    final combined = <SecurityModel>[
      ...exactMatches,
      ...prefixMatches,
      ...substringMatches,
      ...nameMatches,
      ...fnoMatches, // only for F&O
    ].toList();

    setResults(combined);
  }

  /// Sort F&O contracts:
  /// - FUT comes before CE/PE
  /// - Earlier expiry first
  /// - Lower strike price first
  /// - CE before PE grouping
  static List<SecurityModel> sortByFNOFields(List<SecurityModel> securities) {
    securities.sort((a, b) {
      final isFutA = a.instrumentType.toUpperCase() == 'FUT';
      final isFutB = b.instrumentType.toUpperCase() == 'FUT';

      if (isFutA != isFutB) return isFutA ? -1 : 1;

      final dateA = _parseExpiry(a.expiry);
      final dateB = _parseExpiry(b.expiry);

      if (dateA != null && dateB != null) {
        final expiryCompare = dateA.compareTo(dateB);
        if (expiryCompare != 0) return expiryCompare;
      } else if (dateA == null && dateB != null) {
        return 1;
      } else if (dateA != null && dateB == null) {
        return -1;
      }

      final strikeCompare = a.strike.compareTo(b.strike);
      if (strikeCompare != 0) return strikeCompare;

      return a.instrumentType.compareTo(b.instrumentType); // CE before PE
    });

    return securities;
  }

  /// Converts expiry string to DateTime
  static DateTime? _parseExpiry(String? expiry) {
    if (expiry == null) return null;
    try {
      return DateTime.parse(expiry);
    } catch (_) {
      return null;
    }
  }

  /// Enhanced name field matching that supports partial word matching
  /// For equities: "steel" matches names containing "steel" anywhere
  /// For F&O: exact word matching to avoid "nifty" matching "midcapnifty"
  /// For multi-word queries: checks if all words are present
  static bool _matchesNameField(String name, String query,
      {bool isEquity = true}) {
    final lowerName = name.toLowerCase();
    final lowerQuery = query.toLowerCase().trim();

    if (isEquity) {
      // For equities: use contains matching for company names
      if (lowerName.contains(lowerQuery)) return true;

      // For multi-word queries, check if all words are present in the name
      final queryWords = lowerQuery.split(RegExp(r'\s+'));
      if (queryWords.length > 1) {
        return queryWords.every((word) => lowerName.contains(word));
      }
    } else {
      // For F&O: use exact word matching to avoid substring issues
      // Split name into words and check for exact word matches
      final nameWords = lowerName.split(RegExp(r'\s+'));
      final queryWords = lowerQuery.split(RegExp(r'\s+'));

      // Check if all query words have exact matches in name words
      return queryWords.every(
          (queryWord) => nameWords.any((nameWord) => nameWord == queryWord));
    }

    return false;
  }

  /// Test helper method to expose _matchesNameField for unit testing
  static bool testMatchesNameField(String name, String query,
      {bool isEquity = true}) {
    return _matchesNameField(name, query, isEquity: isEquity);
  }
}
