import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:phoenix/models/voice_command_model.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';

/// Service to handle voice assistant functionality including background commands
class VoiceAssistantService {
  static final VoiceAssistantService _instance = VoiceAssistantService._internal();
  factory VoiceAssistantService() => _instance;
  VoiceAssistantService._internal();

  // Core services
  final FlutterTts _flutterTts = FlutterTts();
  
  // State management
  bool _isInitialized = false;
  ConversationContext? _currentConversation;
  
  // Stream controllers for voice events
  final StreamController<VoiceCommand> _commandController = StreamController<VoiceCommand>.broadcast();

  // Getters for streams
  Stream<VoiceCommand> get commandStream => _commandController.stream;

  // Getters for state
  bool get isInitialized => _isInitialized;
  ConversationContext? get currentConversation => _currentConversation;

  /// Initialize the voice assistant service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialize text to speech
      await _initializeTts();

      _isInitialized = true;
      debugPrint('Voice Assistant: Initialized successfully');
      return true;
    } catch (e) {
      debugPrint('Voice Assistant: Initialization failed - $e');
      return false;
    }
  }

  /// Handle incoming voice commands from Google Assistant
  Future<void> handleAssistantIntent(Map<String, dynamic> intentData) async {
    try {
      debugPrint('Voice Assistant: Handling intent - $intentData');
      
      final command = VoiceCommand.fromAssistantIntent(intentData);
      debugPrint('⭐ Voice Assistant: Parsed command - $command');
      await _processVoiceCommand(command);
    } catch (e) {
      debugPrint('Voice Assistant: Error handling intent - $e');
      await speak('Sorry, I couldn\'t process that command. Please try again.');
    }
  }

  /// Handle deep link voice commands
  Future<void> handleDeepLink(Uri uri) async {
    try {
      debugPrint('Voice Assistant: Handling deep link - $uri');
      
      final command = VoiceCommand.fromDeepLink(uri);
      await _processVoiceCommand(command);
    } catch (e) {
      debugPrint('Voice Assistant: Error handling deep link - $e');
      await speak('Sorry, I couldn\'t process that command. Please try again.');
    }
  }

  /// Note: In-app speech recognition removed to focus on background voice commands
  /// Voice commands will come from Google Assistant via intents and deep links

  /// Speak a response using text-to-speech
  Future<void> speak(String text) async {
    try {
      await _flutterTts.speak(text);
      debugPrint('Voice Assistant: Speaking - $text');
    } catch (e) {
      debugPrint('Voice Assistant: Error speaking - $e');
    }
  }

  /// Process a voice command - ONLY emit to stream, no actual command handling
  Future<void> _processVoiceCommand(VoiceCommand command) async {
    try {
      debugPrint('Voice Assistant: Emitting command to stream - $command');

      // Add to conversation context
      _currentConversation = (_currentConversation ?? ConversationContext())
          .addCommand(command);

      // ONLY emit the command for listeners - VoiceCommandHandler will handle everything
      _commandController.add(command);
      
      debugPrint('Voice Assistant: Command emitted to stream - VoiceCommandHandler will handle order placement');

    } catch (e) {
      debugPrint('Voice Assistant: Error emitting command - $e');
    }
  }

  // Note: Command handling has been moved to VoiceCommandHandler to avoid duplicate order placement
  // This service now only emits commands to the stream and provides voice feedback

  /// Request necessary permissions (for TTS only, since we removed speech-to-text)
  Future<bool> _requestPermissions() async {
    // For background voice commands, permissions are handled by Google Assistant
    // TTS doesn't require special permissions
    return true;
  }

  /// Initialize text-to-speech
  Future<void> _initializeTts() async {
    await _flutterTts.setLanguage('en-US');
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
  }

  // Note: Symbol conversion is now handled by VoiceCommandHandler with proper SecurityListBloc lookup

  /// Save conversation context
  Future<void> _saveConversationContext() async {
    if (_currentConversation == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final contextJson = jsonEncode(_currentConversation!.sessionData);
      await prefs.setString('voice_conversation_context', contextJson);
    } catch (e) {
      debugPrint('Voice Assistant: Error saving conversation context - $e');
    }
  }

  /// Load conversation context
  Future<void> _loadConversationContext() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contextJson = prefs.getString('voice_conversation_context');
      
      if (contextJson != null) {
        final contextData = jsonDecode(contextJson) as Map<String, dynamic>;
        _currentConversation = ConversationContext(sessionData: contextData);
      }
    } catch (e) {
      debugPrint('Voice Assistant: Error loading conversation context - $e');
    }
  }

  /// Dispose of resources
  void dispose() {
    _commandController.close();
    _flutterTts.stop();
  }
}