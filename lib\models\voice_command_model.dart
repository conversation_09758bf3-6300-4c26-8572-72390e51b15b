import 'package:uuid/uuid.dart';

/// Enum for different voice command actions
enum VoiceCommandAction {
  buy,
  sell,
  checkPortfolio,
  getQuote,
  unknown,
}

/// Enum for order types that can be specified via voice
enum VoiceOrderType {
  market,
  limit,
  stopLoss,
  unknown,
}

/// Model representing a parsed voice command
class VoiceCommand {
  final String id;
  final VoiceCommandAction action;
  final String? symbol;
  final int? quantity;
  final VoiceOrderType? orderType;
  final double? price;
  final Map<String, dynamic> context;
  final DateTime timestamp;
  final String? originalText;
  final String? conversationId;

  VoiceCommand({
    String? id,
    required this.action,
    this.symbol,
    this.quantity,
    this.orderType,
    this.price,
    Map<String, dynamic>? context,
    DateTime? timestamp,
    this.originalText,
    this.conversationId,
  })  : id = id ?? const Uuid().v4(),
        context = context ?? {},
        timestamp = timestamp ?? DateTime.now();

  /// Create VoiceCommand from Google Assistant intent data
  factory VoiceCommand.fromAssistantIntent(Map<String, dynamic> intentData) {
    final action = _parseAction(intentData['action'] as String?);
    final symbol = intentData['symbol'] as String?;
    final quantity = _parseQuantity(intentData['quantity']);
    final orderType = _parseOrderType(intentData['type'] as String?);
    final price = _parsePrice(intentData['price']);

    return VoiceCommand(
      action: action,
      symbol: symbol?.toUpperCase(),
      quantity: quantity,
      orderType: orderType,
      price: price,
      context: intentData,
      originalText: intentData['originalText'] as String?,
    );
  }

  /// Create VoiceCommand from deep link URI
  factory VoiceCommand.fromDeepLink(Uri uri) {
    final pathSegments = uri.pathSegments;
    final queryParams = uri.queryParameters;

    VoiceCommandAction action = VoiceCommandAction.unknown;
    if (pathSegments.isNotEmpty) {
      switch (pathSegments.first.toLowerCase()) {
        case 'buy':
          action = VoiceCommandAction.buy;
          break;
        case 'sell':
          action = VoiceCommandAction.sell;
          break;
        case 'portfolio':
          action = VoiceCommandAction.checkPortfolio;
          break;
        case 'quote':
          action = VoiceCommandAction.getQuote;
          break;
      }
    }

    return VoiceCommand(
      action: action,
      symbol: queryParams['symbol']?.toUpperCase(),
      quantity: _parseQuantity(queryParams['quantity']),
      orderType: _parseOrderType(queryParams['type']),
      price: _parsePrice(queryParams['price']),
      context: {'source': 'deeplink', 'uri': uri.toString()},
    );
  }

  /// Check if the command has all required parameters for execution
  bool get isComplete {
    switch (action) {
      case VoiceCommandAction.buy:
      case VoiceCommandAction.sell:
        return symbol != null && quantity != null && quantity! > 0;
      case VoiceCommandAction.getQuote:
        return symbol != null;
      case VoiceCommandAction.checkPortfolio:
        return true; // No additional parameters needed
      case VoiceCommandAction.unknown:
        return false;
    }
  }

  /// Get missing parameters for the command
  List<String> get missingParameters {
    final missing = <String>[];
    
    switch (action) {
      case VoiceCommandAction.buy:
      case VoiceCommandAction.sell:
        if (symbol == null) missing.add('stock symbol');
        if (quantity == null || quantity! <= 0) missing.add('quantity');
        break;
      case VoiceCommandAction.getQuote:
        if (symbol == null) missing.add('stock symbol');
        break;
      case VoiceCommandAction.checkPortfolio:
      case VoiceCommandAction.unknown:
        break;
    }
    
    return missing;
  }

  /// Convert to JSON for storage/transmission
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'action': action.name,
      'symbol': symbol,
      'quantity': quantity,
      'orderType': orderType?.name,
      'price': price,
      'context': context,
      'timestamp': timestamp.toIso8601String(),
      'originalText': originalText,
      'conversationId': conversationId,
    };
  }

  /// Create from JSON
  factory VoiceCommand.fromJson(Map<String, dynamic> json) {
    return VoiceCommand(
      id: json['id'] as String,
      action: VoiceCommandAction.values.firstWhere(
        (e) => e.name == json['action'],
        orElse: () => VoiceCommandAction.unknown,
      ),
      symbol: json['symbol'] as String?,
      quantity: json['quantity'] as int?,
      orderType: json['orderType'] != null
          ? VoiceOrderType.values.firstWhere(
              (e) => e.name == json['orderType'],
              orElse: () => VoiceOrderType.unknown,
            )
          : null,
      price: json['price'] as double?,
      context: Map<String, dynamic>.from(json['context'] ?? {}),
      timestamp: DateTime.parse(json['timestamp'] as String),
      originalText: json['originalText'] as String?,
      conversationId: json['conversationId'] as String?,
    );
  }

  @override
  String toString() {
    return 'VoiceCommand(id: $id, action: $action, symbol: $symbol, quantity: $quantity, orderType: $orderType, price: $price)';
  }

  /// Helper methods for parsing
  static VoiceCommandAction _parseAction(String? actionString) {
    if (actionString == null) return VoiceCommandAction.unknown;
    
    switch (actionString.toLowerCase()) {
      case 'com.phoenix.buy_stock':
      case 'buy':
        return VoiceCommandAction.buy;
      case 'com.phoenix.sell_stock':
      case 'sell':
        return VoiceCommandAction.sell;
      case 'com.phoenix.check_portfolio':
      case 'checkportfolio':
      case 'portfolio':
        return VoiceCommandAction.checkPortfolio;
      case 'com.phoenix.get_quote':
      case 'getquote':
      case 'quote':
        return VoiceCommandAction.getQuote;
      default:
        return VoiceCommandAction.unknown;
    }
  }

  static int? _parseQuantity(dynamic quantity) {
    if (quantity == null) return null;
    if (quantity is int) return quantity;
    if (quantity is String) return int.tryParse(quantity);
    return null;
  }

  static double? _parsePrice(dynamic price) {
    if (price == null) return null;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) return double.tryParse(price);
    return null;
  }

  static VoiceOrderType? _parseOrderType(String? type) {
    if (type == null) return null;
    
    switch (type.toLowerCase()) {
      case 'market':
        return VoiceOrderType.market;
      case 'limit':
        return VoiceOrderType.limit;
      case 'stop':
      case 'stoploss':
      case 'stop_loss':
        return VoiceOrderType.stopLoss;
      default:
        return VoiceOrderType.unknown;
    }
  }
}

/// Model for conversation context to handle multi-turn conversations
class ConversationContext {
  final String conversationId;
  final List<VoiceCommand> commandHistory;
  final Map<String, dynamic> sessionData;
  final DateTime startTime;
  final DateTime lastActivity;

  ConversationContext({
    String? conversationId,
    List<VoiceCommand>? commandHistory,
    Map<String, dynamic>? sessionData,
    DateTime? startTime,
    DateTime? lastActivity,
  })  : conversationId = conversationId ?? const Uuid().v4(),
        commandHistory = commandHistory ?? [],
        sessionData = sessionData ?? {},
        startTime = startTime ?? DateTime.now(),
        lastActivity = lastActivity ?? DateTime.now();

  /// Add a command to the conversation history
  ConversationContext addCommand(VoiceCommand command) {
    return ConversationContext(
      conversationId: conversationId,
      commandHistory: [...commandHistory, command],
      sessionData: sessionData,
      startTime: startTime,
      lastActivity: DateTime.now(),
    );
  }

  /// Check if conversation is still active (within 5 minutes)
  bool get isActive {
    return DateTime.now().difference(lastActivity).inMinutes < 5;
  }

  /// Get the last command in the conversation
  VoiceCommand? get lastCommand {
    return commandHistory.isNotEmpty ? commandHistory.last : null;
  }
}