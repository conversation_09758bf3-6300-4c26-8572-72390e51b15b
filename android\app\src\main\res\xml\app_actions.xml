<?xml version="1.0" encoding="utf-8"?>
<actions>
    <!-- Buy Stock Action -->
    <action intentName="actions.intent.OPEN_APP_FEATURE">
        <fulfillment urlTemplate="phoenix://voice/buy?feature=buy_stock">
            <parameter-mapping 
                intentParameter="feature.name" 
                urlParameter="feature" />
        </fulfillment>
        <parameter name="feature.name">
            <entity-set-reference entitySetId="BuyStockFeature"/>
        </parameter>
    </action>

    <!-- Sell Stock Action -->
    <action intentName="actions.intent.OPEN_APP_FEATURE">
        <fulfillment urlTemplate="phoenix://voice/sell?feature=sell_stock">
            <parameter-mapping 
                intentParameter="feature.name" 
                urlParameter="feature" />
        </fulfillment>
        <parameter name="feature.name">
            <entity-set-reference entitySetId="SellStockFeature"/>
        </parameter>
    </action>

    <!-- Portfolio Action -->
    <action intentName="actions.intent.OPEN_APP_FEATURE">
        <fulfillment urlTemplate="phoenix://voice/portfolio?feature=portfolio">
            <parameter-mapping 
                intentParameter="feature.name" 
                urlParameter="feature" />
        </fulfillment>
        <parameter name="feature.name">
            <entity-set-reference entitySetId="PortfolioFeature"/>
        </parameter>
    </action>

    <!-- Stock Quote Action -->
    <action intentName="actions.intent.GET_THING">
        <fulfillment urlTemplate="phoenix://voice/quote?thing={thing.name}">
            <parameter-mapping 
                intentParameter="thing.name" 
                urlParameter="thing" />
        </fulfillment>
        <parameter name="thing.name">
            <entity-set-reference entitySetId="StockQuote"/>
        </parameter>
    </action>

    <!-- Entity Sets -->
    <entity-set entitySetId="BuyStockFeature">
        <entity name="buy_stock" alternateName="@array/buy_stock_synonyms"/>
    </entity-set>

    <entity-set entitySetId="SellStockFeature">
        <entity name="sell_stock" alternateName="@array/sell_stock_synonyms"/>
    </entity-set>

    <entity-set entitySetId="PortfolioFeature">
        <entity name="portfolio" alternateName="@array/portfolio_synonyms"/>
    </entity-set>

    <entity-set entitySetId="StockQuote">
        <entity name="stock_quote" alternateName="@array/stock_quote_synonyms"/>
    </entity-set>
</actions>