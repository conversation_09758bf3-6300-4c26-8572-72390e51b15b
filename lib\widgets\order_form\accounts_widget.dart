import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
//For this to work we nned to do some changes in the local files downloaded.

class AccountsWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12),
      margin: EdgeInsets.all(12),
      width: 348,
      height: 148,
      decoration: BoxDecoration(
        color: Color(0xff383838),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            blurStyle: BlurStyle.solid,
            color: const Color(0xFFE5E5E5).withOpacity(0.4),
            offset: const Offset(-10, -10),
            blurRadius: 20,
            inset: true,
          ),
          BoxShadow(
            blurStyle: BlurStyle.solid,
            color: const Color(0xFF383838),
            offset: const Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],

      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Accounts',
                style: TextStyle(
                  color: Color(0xffCDCDCD),
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 4),
              Icon(
                Icons.info_outline,
                color: Colors.blue[300],
                size: 18,
              ),
            ],
          ),
          SizedBox(height: 4),
          Wrap(
            //space btw column
            spacing: 29,
            //space btw row
            runSpacing: 12,
            children: [
              _buildAccountItem(Colors.blue, 'UX4521', '150'),
              _buildAccountItem(Colors.red, 'J45H5', '45'),
              _buildAccountItem(Colors.red, 'J5N45', '400'),
              _buildAccountItem(Colors.white, 'J4JK3J', '485'),
              _buildAccountItem(Colors.blue[300]!, 'BP4521', '120'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountItem(Color iconColor, String id, String quantity) {
    return SizedBox(
      width: 120,
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: iconColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: 4),
          Text(
            '($id)',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 15,
            ),
          ),
          Spacer(),
          Text(
            quantity,
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 15,
            ),
          ),
        ],
      ),
    );
  }
}