import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';

class CustomSearchBar extends StatelessWidget {
  final ValueChanged<String> onSearch;
  final String hintText;
  final TextEditingController? controller;
  final bool autofocus;
  final VoidCallback? onClose;

  const CustomSearchBar({
    super.key,
    required this.onSearch,
    this.hintText = 'Search...',
    this.controller,
    this.autofocus = false,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          height: 40,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(10),
            // boxShadow: ThemeConstants.neomorpicShadow,
          ),
      child: TextField(
        controller: controller,
        autofocus: autofocus,
        textAlignVertical: TextAlignVertical.center,
        cursorColor: AppTheme.primaryColor(themeState.isDarkMode),
        style: TextStyle(
          color: AppTheme.textPrimary(themeState.isDarkMode),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: AppTheme.textSecondary(themeState.isDarkMode),
            fontSize: 16,
          ),
          prefixIcon: Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Icon(
              Icons.search,
              color: AppTheme.textSecondary(themeState.isDarkMode),
              size: 20,
            ),
          ),
          suffixIcon: onClose != null ? Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: IconButton(
              icon: Icon(
                Icons.close,
                color: AppTheme.textSecondary(themeState.isDarkMode),
                size: 20,
              ),
              onPressed: onClose,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ) : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
          /// this is used to aling the text and icon in same line
          isDense: false,
        ),
        onChanged: onSearch,
      ),
        );
      },
    );
  }
}


