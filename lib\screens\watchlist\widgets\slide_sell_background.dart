import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';

class SlideSellBackground extends StatelessWidget {
  const SlideSellBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.sellSlideOptionColor,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(10),
          right: Radius.circular(10),
        ),
        boxShadow: [
          // Light inner glow near edges
          BoxShadow(
            color: Colors.red.shade200,
            offset: const Offset(-10, -10),
            blurRadius: 20,
            inset: true,
          ),
          // Dark inner shadow in center-ish
          const BoxShadow(
            color: Colors.black,
            offset: Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],
      ),
      child: const Align(
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: 20),
            Text(
              "Sell",
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w800,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    );
  }
}