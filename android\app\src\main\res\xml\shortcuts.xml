<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Buy Stock Shortcut -->
    <shortcut
        android:shortcutId="buy_stock"
        android:enabled="true"
        android:icon="@mipmap/ic_launcher"
        android:shortcutShortLabel="@string/buy_stock_short"
        android:shortcutLongLabel="@string/buy_stock_long">
        <intent
            android:action="com.phoenix.BUY_STOCK"
            android:targetPackage="com.zentropytech.phoenix"
            android:targetClass="com.zentropytech.phoenix.MainActivity" />
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.BUY_STOCK">
            <parameter-binding
                android:key="stock.symbol"
                android:value="@string/symbol_parameter" />
            <parameter-binding
                android:key="stock.quantity"
                android:value="@string/quantity_parameter" />
        </capability-binding>
    </shortcut>

    <!-- Sell Stock Shortcut -->
    <shortcut
        android:shortcutId="sell_stock"
        android:enabled="true"
        android:icon="@mipmap/ic_launcher"
        android:shortcutShortLabel="@string/sell_stock_short"
        android:shortcutLongLabel="@string/sell_stock_long">
        <intent
            android:action="com.phoenix.SELL_STOCK"
            android:targetPackage="com.zentropytech.phoenix"
            android:targetClass="com.zentropytech.phoenix.MainActivity" />
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.SELL_STOCK">
            <parameter-binding
                android:key="stock.symbol"
                android:value="@string/symbol_parameter" />
            <parameter-binding
                android:key="stock.quantity"
                android:value="@string/quantity_parameter" />
        </capability-binding>
    </shortcut>

    <!-- Check Portfolio Shortcut -->
    <shortcut
        android:shortcutId="check_portfolio"
        android:enabled="true"
        android:icon="@mipmap/ic_launcher"
        android:shortcutShortLabel="@string/portfolio_short"
        android:shortcutLongLabel="@string/portfolio_long">
        <intent
            android:action="com.phoenix.CHECK_PORTFOLIO"
            android:targetPackage="com.zentropytech.phoenix"
            android:targetClass="com.zentropytech.phoenix.MainActivity" />
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.CHECK_PORTFOLIO" />
    </shortcut>

    <!-- Get Quote Shortcut -->
    <shortcut
        android:shortcutId="get_quote"
        android:enabled="true"
        android:icon="@mipmap/ic_launcher"
        android:shortcutShortLabel="@string/quote_short"
        android:shortcutLongLabel="@string/quote_long">
        <intent
            android:action="com.phoenix.GET_QUOTE"
            android:targetPackage="com.zentropytech.phoenix"
            android:targetClass="com.zentropytech.phoenix.MainActivity" />
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.GET_QUOTE">
            <parameter-binding
                android:key="stock.symbol"
                android:value="@string/symbol_parameter" />
        </capability-binding>
    </shortcut>
</shortcuts>